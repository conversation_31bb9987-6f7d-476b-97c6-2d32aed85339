<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.workplat</groupId>
        <artifactId>ai-central-platform</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>accept-dubbo</artifactId>
    <packaging>jar</packaging>
    <description>dubbo服务</description>

    <dependencies>
        <!-- gss-all -->
        <dependency>
            <groupId>com.workplat</groupId>
            <artifactId>gss-all</artifactId>
        </dependency>
        <!--word导出 -start-->
        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
        </dependency>
        <!--word导出 -end-->
    </dependencies>


</project>
