package com.workplat.accept.business.home.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

/**
 * @Author: Odin
 * @Date: 2024/10/19 21:38
 * @Description:
 */
@Data
public class ConfMatterCatalogDto {

    private String id;

    /**
     * 事项名称
     */
    @Schema(description = "事项名称")
    private String matterName;

    /**
     * 事项编码
     */
    @Schema(description = "事项编码")
    private String matterCode;

    /**
     * 事项层级
     */
    @Schema(description = "事项层级")
    private String matterLevel;

    /**
     * 事项类型
     */
    @Schema(description = "事项类型")
    private String matterType;

    /**
     * 是否发布
     */
    @Schema(description = "是否发布")
    private String isPublic;

}
