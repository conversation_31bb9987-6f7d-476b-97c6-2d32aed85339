package com.workplat.accept.business.chat.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

/**
 * @ClassName: StreamWorkFlowResponse
 * @Description:
 * @Author: <PERSON>
 * @Date: 2025-03-10 17:32
 * @Version
 **/
@Data
public class StreamWorkFlowVO {

    /**
     * 不同模式下的事件类型.
     */
    private String event;

    /**
     * 任务ID.
     */
    @JsonProperty(value = "task_id")
    private String taskId;

    /**
     *  workflow 执行 ID
     */
    @JsonProperty(value = "workflow_run_id")
    private String workflowRunId;

    /**
     *  详情内容
     */
    private Map<String, Object> data;
}
