package com.workplat.accept.business.serve.vo;


import com.workplat.gss.common.core.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
public class BizServeMethodVo implements Serializable {

    @Schema(description = "id")
    private String id;

    @Schema(description = "服务")
    private String serveInfoId;

    @Schema(description = "服务")
    private Integer sort;

    @Dict(dictCode = "serve_method")
    @Schema(description = "渠道类型")
    private String type;

    @Schema(description = "渠道类型编码")
    private String typeCode;

    @Schema(description = "图标")
    private String iconId;

    @Schema(description = "渠道描述")
    private String description;

    @Schema(description = "内容")
    private String content;

    @Schema(description = "认证等级")
    private Integer certificationLevel;

}
