package com.workplat.accept.business.home.service;

import com.workplat.accept.business.home.dto.ConfMatterCatalogDto;
import com.workplat.accept.business.home.dto.HomeConfDto;
import com.workplat.accept.business.home.entity.HomeConf;
import com.workplat.accept.business.home.vo.ConfMatterCatalogVO;
import com.workplat.accept.business.home.vo.HomeConfVO;
import com.workplat.gss.common.core.service.BaseService;
import jakarta.validation.Valid;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * @Author: Odin
 * @Date: 2024/9/24 16:25
 * @Description:
 */
public interface HomeConfService extends BaseService<HomeConf> {

    LinkedHashMap<String, List<HomeConfVO>> getHomeConfList();

    void saveHomeConf(@Valid HomeConfDto homeConfDto);

    HomeConfVO queryByConfMatterId(String confMatterId);

    void deleteMatter(String id);

    ConfMatterCatalogVO saveMatter(ConfMatterCatalogDto confMatterCatalogDto);
}
