package com.workplat.accept.business.chat.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@Schema(description = "办件记录VO")
public class BizInstanceChatVO {

    @Schema(description = "办件记录ID")
    private String id;

    @Schema(description = "办件ID")
    private String instanceId;

    @Schema(description = "当前节点")
    private String currentNode;

    @Schema(description = "当前节点名称")
    private String currentNodeName;

    @Schema(description = "办件编号")
    private String no;

    @Schema(description = "办件名称")
    private String name;

    @Schema(description = "办件时间")
    private Date time;

    @Schema(description = "办件人名称")
    private String userName;

    @Schema(description = "渠道（allinone/local）")
    private String channel;

    @Schema(description = "跳转链接")
    private String skipUrl;

    @Schema(description = "手机链接")
    private String mobileUrl;

}
