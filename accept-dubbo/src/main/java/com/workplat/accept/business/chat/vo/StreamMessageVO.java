package com.workplat.accept.business.chat.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;


/**
 * Dify流式调用响应.
 */
//@JsonPropertyOrder(value = {"event", "content", "thoughts", "config"}, alphabetic = false)
@Data
public class StreamMessageVO implements Serializable {

    /**
     * 不同模式下的事件类型.
     */
    private String event;

    /**
     * agent_thought id.
     */
    private String id;

    /**
     * 任务ID.
     */
    @JsonProperty(value = "task_id")
    private String taskId;

    /**
     * 消息唯一ID.
     */
    @JsonProperty(value = "message_id")
    private String messageId;

    /**
     * LLM 返回文本块内容.
     */
    private String answer;

    /**
     * 用户输入
     */
    private String query;

    /**
     * 创建时间戳.
     */
    @JsonProperty(value = "created_at")
    private Long createdAt;

    /**
     * 会话 ID.
     */
    @JsonProperty(value = "conversation_id")
    private String conversationId;

    /**
     * 元数据.
     */
    private Map<String, Object> metadata;

    /**
     * 内容
     */
    private String content;

    /**
     * 思考
     */
    private String thoughts;

    /**
     * 配置
     */
    private String config;

    /**
     * 记录id,作为多个智能体之间的唯一标识符
     */
    private String recordId;

}
