package com.workplat.accept.business.chat.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: FileClassifyRequest
 * @Description:
 * @Author: Yang Fan
 * @Date: 2025-03-09 15:57
 * @Version
 **/
@Builder
@Data
public class FileClassifyDTO {

    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private String userId;

    /**
     * 会话id
     */
    @Schema(description = "会话id")
    private String chatId;

    /**
     * 材料id
     */
    @Schema(description = "材料id")
    private List<String> materialIds;

    /**
     * 文件
     */
    @Schema(description = "文件")
    private List<FileDTO> fileUrls;

    /**
     * 需要识别的字段（仅用于识别文件内容情形，不适用于文件分类情形）
     */
    @Schema(description = "需要识别的字段")
    private String recognizeContent;
}
