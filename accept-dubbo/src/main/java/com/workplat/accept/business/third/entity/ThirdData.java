package com.workplat.accept.business.third.entity;

import com.workplat.gss.common.core.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Comment;

import java.time.LocalDateTime;

/**
 * @Author: Odin
 * @Date: 2024/9/25 14:18
 * @Description:
 */

@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Entity
@Table(name = "cus_third_data")
@Data
@NoArgsConstructor
public class ThirdData extends BaseEntity {

    @Comment("业务编号")
    @Column(name = "business_id", length = 50)
    private String businessId;

    @Comment("合同编号")
    @Column(name = "contract_no", length = 32)
    private String contractNo;

    @Comment("事项名称")
    @Column(name = "item_name", length = 32)
    private String itemName;

    @Comment("申报来源")
    @Column(name = "item_source", length = 32)
    private String itemSource;

    @Comment("申请对象名称")
    @Column(name = "apply_name", length = 50)
    private String applyName;

    @Comment("申请对象联系方式")
    @Column(name = "apply_phone", length = 100)
    private String applyPhone;

    @Comment("申请时间")
    @Column(name = "apply_time")
    private LocalDateTime applyTime;

    @Comment("办件状态 0:已通过 1:审核中 2:已退回")
    @Column(name = "apply_status")
    private String applyStatus;

    @Comment("审核意见")
    @Column(name = "apply_remark")
    private String applyRemark;

    @Comment("办件地址")
    @Column(name = "go_url", length = 200)
    private String goUrl;

    @Comment("经办人身份证")
    @Column(name = "agent_card_id",length = 32)
    private String agentCardId;

    @Comment("经办人RMC用户ID")
    @Column(name = "agent_sso_user_id")
    private String agentSsoUserId;

}
