package com.workplat.accept.business.home.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Author: Odin
 * @Date: 2024/10/17 22:14
 * @Description:
 */
@Data
public class HomeConfDto {

    @NotBlank(message = "未关联事项!")
    private String relatedConfMatterId;
    @NotBlank(message = "应用名称不能为空")
    private String matterName;
    @NotBlank(message = "应用类型不能为空")
    private String matterType;
    private String businessDesc;
    @NotBlank(message = "图标icon不能为空")
    private String iconAddress;
    @NotBlank(message = "激活图标icon不能为空")
    private String hoverIconAddress;
    @NotBlank(message = "跳转链接不能为空")
    private String skipUrl;
    private Boolean isExternalLink;
    @NotBlank(message = "请选择菜单是否可用")
    private String menusStatus;
    private String menuRemark;

}
