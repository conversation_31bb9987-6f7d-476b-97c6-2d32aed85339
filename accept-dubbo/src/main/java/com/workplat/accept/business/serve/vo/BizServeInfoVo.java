package com.workplat.accept.business.serve.vo;



import com.workplat.gss.common.core.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BizServeInfoVo implements Serializable {

    @Schema(description = "id")
    private String id;

    @Schema(description = "服务名称")
    private String name;

    /**
     *  服务类型 数据字典:serve_type
     */
    @Dict(dictCode = "serve_type")
    @Schema(description = "服务类型")
    private String type;

    /**
     * 办事指南类型  数据字典:guide_type
     */
    @Schema(description = "办事指南类型")
    private String guideType;

    @Schema(description = "办事指南地址")
    private String guideUrl;

    @Schema(description = "服务描述")
    private String description;

    @Schema(description = "服务编码")
    private String code;

    @Schema(description = "是否启用")
    private boolean enable;

    @Schema(description = "是否第三方服务")
    private boolean thirdParty;

    @Schema(description = "办理方式list")
    private List<BizServeMethodVo> methodList;

    @Schema(description = "事项指南信息")
    private String guideJson;


}
