package com.workplat.accept.business.chat.dto;

import com.workplat.componentEngine.dto.ComponentRunDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;


/**
 * @ClassName: AskRequestBody
 * @Description:
 * @Author: Yang Fan
 * @Date: 2025-02-24 17:29
 * @Version
 **/
@Schema(name = "AskRequestBody")
@Data
public class AskDTO {


    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private String userId;

    /**
     * 会话id
     */
    @Schema(description = "会话id")
    private String chatId;

    /**
     * 问题
     */
    @Schema(description = "问题")
    private String questions;

    /**
     * inputs
     */
    @Schema(description = "inputs")
    private Map<String, Object> inputs;

    /**
     * appKey
     */
    @Schema(description = "appKey")
    private String appKey;


    /**
     * 全局记录id
     */
    @Schema(description = "全局记录id")
    private String recordId;

    /**
     * 文件数据
     */
    @Schema(description = "文件数据")
    private List<FileDTO> fileUrls;


    @Schema(description = "组件运行DTO")
    private ComponentRunDTO componentRunDto;

    /**
     * 渠道
     */
    @Schema(description = "渠道")
    private String channel;


}
