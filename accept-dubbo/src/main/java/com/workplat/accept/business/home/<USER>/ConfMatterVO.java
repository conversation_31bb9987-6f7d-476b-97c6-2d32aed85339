package com.workplat.accept.business.home.vo;

import com.alibaba.fastjson2.annotation.JSONField;
import com.workplat.gss.common.core.annotation.Dict;
import com.workplat.gss.common.core.constant.DateFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: Odin
 * @Date: 2024/10/19 21:51
 * @Description:
 */

@Data
public class ConfMatterVO {
    @Schema(description = "id")
    private String id;
    /**
     * 事项名称
     */
    @Schema(description = "事项名称")
    private String matterName;

    /**
     * 事项编码
     */
    @Schema(description = "事项编码")
    private String matterCode;
    @Schema(description = "事项类型")
    private String matterType;

    /**
     * 所属区域
     */
    @Schema(description = "所属区域")
    @Dict(dictCode = "Region")
    private String region;

    /**
     * 逻辑删除状态
     */
    private boolean deleted;

    /**
     * 排序
     */
    private Integer sort ;

    /**
     * 版本
     */
    private Integer version ;

    /**
     * 创建时间
     */
    @JSONField(format = DateFormat.DATE_MINI_PATTERN)
    private Date createTime;

    /**
     * 更新时间
     */
    @JSONField(format = DateFormat.DATE_MINI_PATTERN)
    private Date updateTime;

    /**
     * 创建人
     */

    private String createdBy;

    /**
     * 更新人
     */

    private String updatedBy;
    /**
     * 是否发布
     */
    @Schema(description = "是否发布")
    private String isPublic;
    /**
     * 是否普通事项
     */
    private Boolean isNormal;

    private List<VersionVO> history;

}
