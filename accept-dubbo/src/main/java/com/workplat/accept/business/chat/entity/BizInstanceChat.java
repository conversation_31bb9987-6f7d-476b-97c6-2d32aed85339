package com.workplat.accept.business.chat.entity;

import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.common.core.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

/**
 * 聊天记录办件 信息
 * <AUTHOR>
 * @date 2025/06/30
 */
@Setter
@Getter
@Entity
@Table(name = "biz_instance_chat", indexes = {
        @Index(name = "deleted", columnList = "deleted")
})
public class BizInstanceChat extends BaseEntity {

    @OneToOne
    @JoinColumn(name = "instance_id")
    @Comment("申报对象")
    @Where(clause = "deleted = 0")
    private BizInstanceInfo instance;

    /**
     * @see com.workplat.accept.business.chat.constant.NodeStatusEnum
     */
    @Column(name = "current_node")
    @Comment("当前节点")
    private String currentNode;

    @Column(name = "current_node_name")
    @Comment("当前节点名称")
    private String currentNodeName;

}
