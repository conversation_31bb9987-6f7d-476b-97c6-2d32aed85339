package com.workplat.accept.user.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: Odin
 * @Date: 2024/9/23 11:51
 * @Description:
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SsoUserVO {

    private String userId;

    private String username;

    private String certificateType;

    private String certificateNumber;

    private String aNetToken;

    private String token;
}
