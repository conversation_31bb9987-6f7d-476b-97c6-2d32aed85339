package com.workplat.componentEngine.vo;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Data
public class InstructionFixedVO {

    private String title;

    private List<ConditionItem> list;


    @Setter
    @Getter
    public static class ConditionItem {
        // Getter and Setter
        private String text;
        private List<Props> props;
    }

    @Setter
    @Getter
    public static class Props {
        // Getter and Setter
        private String text;
        private String sections;

        public Props(String name, String rText) {
            this.text = name;
            this.sections = rText;
        }
    }
}
