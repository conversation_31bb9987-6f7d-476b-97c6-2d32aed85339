# Changelog

## [Unreleased]

### Added
- 增强了FormJsonProcessor的功能，支持新的表单数据格式
  - 添加了对特殊组件（单选框、下拉框）的处理
  - 支持组件状态的维护
  - 改进了值更新逻辑

## 2024-07-01 10:30:00

### 1. 初始化项目文档结构

**变更类型**: docs

> **目的**: 建立项目文档结构，便于团队理解项目和协作
> **详细描述**: 创建了.codelf目录，添加了项目描述、开发注意事项和变更日志文件
> **变更原因**: 需要更好地管理和维护项目文档，提高团队协作效率
> **影响范围**: 项目文档结构
> **API变更**: 无
> **配置变更**: 无
> **性能影响**: 无

   ```
   ai-central-platform
   ├── .codelf                  // add 项目文档目录
   │   ├── project.md           // add 项目描述文件
   │   ├── attention.md         // add 开发注意事项文件
   │   └── _changelog.md        // add 变更日志文件
   ```

### 2. AI对话功能实现

**Change Type**: feature

> **Purpose**: 实现智能问答和组件运行功能
> **Detailed Description**: 在ChatApiImpl中实现了两个核心接口：componentRun（组件运行）和ask（问答功能），通过DifyServiceHelper与Dify平台交互实现AI对话功能
> **Reason for Change**: 为系统添加智能对话能力，提升用户体验
> **Impact Scope**: 仅影响chat相关功能模块
> **API Changes**: 新增/api/chat/component/run和/api/chat/ask两个接口
> **Configuration Changes**: 需配置dify.api参数指向Dify服务地址
> **Performance Impact**: 对外部Dify服务有依赖，需确保服务稳定

   ```
   accept-biz
    ├── ChatApiImpl.java                 // add 实现聊天API接口
    └── DifyServiceHelper.java           // add 封装与Dify平台交互的方法
   ```

### 3. {function simple description}

**Change Type**: {type: feature/fix/improvement/refactor/docs/test/build}

> **Purpose**: {function purpose}
> **Detailed Description**: {function detailed description}
> **Reason for Change**: {why this change is needed}
> **Impact Scope**: {other modules or functions that may be affected by this change}
> **API Changes**: {if there are API changes, detail the old and new APIs}
> **Configuration Changes**: {changes to environment variables, config files, etc.}
> **Performance Impact**: {impact of the change on system performance}

   ```
   root
   - pkg    // {type: add/del/refact/-} {The role of a folder}
    - utils // {type: add/del/refact} {The function of the file}
   - xxx    // {type: add/del/refact} {The function of the file}
   ```