<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!--日志格式应用spring boot默认的格式，也可以自己更改-->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <!--定义日志存放的位置，默认存放在项目启动的相对路径的目录-->
    <springProperty scope="context" name="LOG_PATH" source="log.path" defaultValue="logs"/>
    <springProperty name="APP_NAME" source="spring.application.name"/>


    <!-- dev环境只记录日志到控制台 -->
    <springProfile name="dev">
        <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>${CONSOLE_LOG_PATTERN}</pattern>
                <charset>utf-8</charset>
            </encoder>
        </appender>
        <logger name="com.alibaba.cloud.nacos.client">
            <level value="DEBUG"/>
        </logger>
        <!--默认所有的包以info-->
        <root level="info">
            <appender-ref ref="STDOUT"/>
        </root>
    </springProfile>

    <!-- 非dev环境记录所有日志 -->
    <springProfile name="!dev">

        <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>${CONSOLE_LOG_PATTERN}</pattern>
                <charset>utf-8</charset>
            </encoder>
        </appender>

        <!-- 日志记录器，日期滚动记录 -->
        <appender name="FILE_ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <!-- 正在记录的日志文件的路径及文件名 -->
            <file>${LOG_PATH}/${APP_NAME}/log_error.log</file>
            <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
            <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <FileNamePattern>${LOG_PATH}/${APP_NAME}/log-error-%d{yyyy-MM-dd}.%i.log</FileNamePattern>
                <maxFileSize>100MB</maxFileSize>
                <MaxHistory>30</MaxHistory>
                <totalSizeCap>20GB</totalSizeCap>
                <cleanHistoryOnStart>false</cleanHistoryOnStart>
            </rollingPolicy>
            <!-- 追加方式记录日志 -->
            <append>true</append>

            <!-- 日志文件的格式 -->
            <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
                <pattern>${FILE_LOG_PATTERN}</pattern>
                <charset>utf-8</charset>
            </encoder>

            <!-- 此日志文件只记录error级别的 -->
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>error</level>
                <onMatch>ACCEPT</onMatch>
                <onMismatch>DENY</onMismatch>
            </filter>
        </appender>

        <!-- 日志记录器，日期滚动记录 -->
        <appender name="FILE_ALL" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <!-- 正在记录的日志文件的路径及文件名 -->
            <file>${LOG_PATH}/${APP_NAME}/log_all.log</file>
            <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
            <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <FileNamePattern>${LOG_PATH}/${APP_NAME}/log-all-%d{yyyy-MM-dd}.%i.log</FileNamePattern>
                <maxFileSize>100MB</maxFileSize>
                <MaxHistory>30</MaxHistory>
                <totalSizeCap>20GB</totalSizeCap>
                <cleanHistoryOnStart>false</cleanHistoryOnStart>
            </rollingPolicy>
            <!-- 追加方式记录日志 -->
            <append>true</append>
            <!-- 日志文件的格式 -->
            <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
                <pattern>${FILE_LOG_PATTERN}</pattern>
                <charset>utf-8</charset>
            </encoder>
        </appender>

        <!--记录到文件时，记录两类一类是error日志，一个是所有日志-->
        <root level="info">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="FILE_ERROR"/>
            <appender-ref ref="FILE_ALL"/>
        </root>

    </springProfile>

</configuration>