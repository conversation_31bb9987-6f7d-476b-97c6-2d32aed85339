package com.workplat.matter.api;

/**
 * <AUTHOR> cheng
 * @package com.workplat.matter.api
 * @description
 * @date 2025/5/21 16:38
 */

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Maps;
import com.workplat.accept.business.serve.entity.BizServeInfo;
import com.workplat.accept.business.serve.entity.BizServeMethod;
import com.workplat.flow.service.ConfFlowService;
import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.exception.BusinessException;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.common.core.util.RedisUtil;
import com.workplat.gss.log.annotation.ApiLogging;
import com.workplat.gss.log.constant.OperationType;
import com.workplat.gss.service.item.dubbo.matter.entity.ConfMatter;
import com.workplat.gss.service.item.dubbo.matter.service.ConfMatterService;
import com.workplat.matter.entity.ConfMatterExtend;
import com.workplat.matter.service.ConfMatterExtendService;
import com.workplat.matter.vo.ConfMatterExtendDTO;
import com.workplat.matter.vo.ConfMatterExtendVO;
import com.workplat.serve.dto.MethodDefaultDTO;
import com.workplat.serve.service.BizServeInfoService;
import com.workplat.serve.service.BizServeMethodService;
import jodd.util.StringUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.transaction.annotation.Transactional;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


@RestController
public class MatterInfoApiImpl implements MatterInfoApi{

    @Autowired
    private ConfMatterExtendService confMatterExtendService;
    @Autowired
    private ConfMatterService confMatterService;
    @Autowired
    private ConfFlowService confFlowService;
    @Autowired
    private BizServeInfoService serveInfoService;
    @Autowired
    private BizServeMethodService  serveMethodService;
    @Autowired
    private RedisUtil redisUtil;

    private static String serveRedisKey = "serve_method_default";


    @Override
    @ApiLogging(module = "边聊边办事项信息模块", operation = "通过名称获取事项信息", type = OperationType.QUERY)
    public ResponseData<ConfMatterExtendVO> getByName(String name) {
        ConfMatterExtend extend = confMatterExtendService.queryForSingle(MapUtil.<String, Object>builder()
                .put("=(matter.matterName)", name)
                .put("=(matter.isPublic)", "1")
                .build());
        if (extend == null){
            return ResponseData.success().build();
        }
        return ResponseData.success(convert(extend));
    }

    @Override
    @ApiLogging(module = "边聊边办事项信息模块", operation = "通过事项id获取扩展信息", type = OperationType.QUERY)
    public ResponseData<ConfMatterExtendVO> getByMatterId(String matterId) {
        ConfMatterExtend extend = confMatterExtendService.queryForSingle(MapUtil.<String, Object>builder()
                .put("=(matter.id)", matterId)
                .build());
        if (extend != null){
            return ResponseData.success(convert(extend));
        }
        ConfMatter matter = confMatterService.queryById(matterId);
        if (matter == null){
            throw new BusinessException("事项不存在");
        }
        ConfMatterExtendVO vo  = new ConfMatterExtendVO();
        vo.setMatterId(matterId);
        vo.setMatterName(matter.getMatterName());
        vo.setMatterCode(matter.getMatterCode());
        return ResponseData.success(new ConfMatterExtendVO());
    }

    @Override
    @ApiLogging(module = "边聊边办事项信息模块", operation = "通过事项code获取扩展信息", type = OperationType.QUERY)
    public ResponseData<ConfMatterExtendVO> getByMatterCode(String matterCode) {
        ConfMatterExtend extend = confMatterExtendService.queryForSingle(MapUtil.<String, Object>builder()
                .put("=(matter.matterCode)", matterCode)
                .build());
        if (extend != null){
            return ResponseData.success(convert(extend));
        }
        ConfMatter matter = confMatterService.queryForSingle(MapUtil.<String, Object>builder()
                .put("=(matterCode)", matterCode)
                .build());
        if (matter == null){
            throw new BusinessException("事项不存在");
        }
        ConfMatterExtendVO vo  = new ConfMatterExtendVO();
        vo.setMatterId(matter.getId());
        vo.setMatterName(matter.getMatterName());
        vo.setMatterCode(matter.getMatterCode());
        return ResponseData.success(new ConfMatterExtendVO());
    }

    @Override
    @ApiLogging(module = "边聊边办事项信息模块", operation = "保存事项扩展信息", type = OperationType.INSERT)
    @Transactional(rollbackFor = Exception.class)
    public ResponseData<Void> saveExtend(ConfMatterExtendDTO dto) {
        ConfMatter matter = confMatterService.queryForSingle(MapUtil.<String, Object>builder().put("=(matterCode)", dto.getMatterCode()).build());
        if (matter == null){
            throw new BusinessException("事项不存在");
        }
        ConfMatterExtend extend = new ConfMatterExtend();
        ConfMatterExtend old = confMatterExtendService.queryForSingle(MapUtil.<String, Object>builder()
                .put("=(matter.matterCode)", dto.getMatterCode())
                .build());
        if (old != null){
            extend = old;
        }
        if (StringUtils.isNotBlank(dto.getFlowCode())){
            extend.setConfFlow(confFlowService.queryForSingle(MapUtil.<String, Object>builder().put("=(code)", dto.getFlowCode()).build()));
        }
        if (StringUtils.isNotBlank(dto.getPcFlowCode())){
            extend.setPcConfFlow(confFlowService.queryForSingle(MapUtil.<String, Object>builder().put("=(code)", dto.getPcFlowCode()).build()));
        }
        if (StringUtils.isNotBlank(dto.getInstructionRemoteTip())){
            extend.setInstructionRemoteTip(dto.getInstructionRemoteTip());
        }
        if (StringUtils.isNotBlank(dto.getInformedConsent())){
            extend.setInformedConsent(dto.getInformedConsent());
        }
        extend.setMatter(matter);
        extend.setServeList(dto.getServeIds());
        extend.setInformAfterSubmit(dto.getInformAfterSubmit());
        confMatterExtendService.save(extend);
        // 作为事项保存到边聊边办服务中
        BizServeInfo serveInfo = serveInfoService.queryForSingle(MapUtil.<String, Object>builder().put("=(name)", matter.getMatterName()).build());
        if (serveInfo == null){
            serveInfo = new BizServeInfo();
            serveInfo.setCode(matter.getId());
            serveInfo.setName(matter.getMatterName());
            serveInfo.setType("item");
            serveInfo.setThirdParty(false);
            serveInfo.setEnable(true);

            BizServeMethod method = new BizServeMethod();
            method.setContent("我要办理" + matter.getMatterName());
            method.setType("LTB");
            method.setServe(serveInfo);

            // 从redis中获取服务方式默认值
            List<Object> list = redisUtil.lGet(serveRedisKey, 0, -1);
            if (list != null && !list.isEmpty()){
                List<MethodDefaultDTO> dtoList = JSONArray.parseArray(JSONArray.toJSONString(list), MethodDefaultDTO.class);
                for (MethodDefaultDTO methodDefaultDTO : dtoList) {
                    if ("LTB".equals(methodDefaultDTO.getMethod())){
                        method.setDescription(methodDefaultDTO.getDescription());
                        method.setIconId(methodDefaultDTO.getIconId());
                        break;
                    }
                }
            }
            serveMethodService.save(method);
            serveInfo.setMethodList(List.of(method));
            serveInfoService.save(serveInfo);
        }
        return ResponseData.success().build();
    }

    @Override
    @ApiLogging(module = "边聊边办事项信息模块", operation = "分页查询边聊边办事项清单", type = OperationType.QUERY)
    public ResponseData<Page<ConfMatterExtendVO>> queryItemPage(PageableDTO pageDTO, String keyword) {
        Map<String, Object> param = Maps.newHashMap();
        param.put("=(matter.deleted)", Boolean.FALSE);
        if (StringUtil.isNotBlank(keyword)) {
            param.put("like(name)", keyword);
        }
        Page<ConfMatterExtend> page = confMatterExtendService.queryForPage(param, pageDTO.convertPageable());
        List<ConfMatterExtendVO> list = Lists.newArrayList();
        page.getContent().forEach(extend -> {
            ConfMatterExtendVO vo = new ConfMatterExtendVO();
            vo.setMatterId(extend.getMatter().getId());
            vo.setMatterName(extend.getMatter().getMatterName());
            list.add(vo);
        });
        return ResponseData.success(new PageImpl<>(list, page.getPageable(), page.getTotalElements()));
    }

    private ConfMatterExtendVO convert(ConfMatterExtend extend){
        ConfMatterExtendVO vo = new ConfMatterExtendVO();
        vo.setMatterName(extend.getMatter().getMatterName());
        vo.setMatterCode(extend.getMatter().getMatterCode());
        vo.setMatterId(extend.getMatter().getId());
        vo.setInformedConsent(extend.getInformedConsent());
        vo.setInformAfterSubmit(extend.getInformAfterSubmit());
        vo.setInstructionRemoteTip(extend.getInstructionRemoteTip());
        if (extend.getConfFlow() != null){
            vo.setFlowName(extend.getConfFlow().getName());
            vo.setFlowCode(extend.getConfFlow().getCode());
        }
        if (extend.getPcConfFlow() != null){
            vo.setPcFlowName(extend.getPcConfFlow().getName());
            vo.setPcFlowCode(extend.getPcConfFlow().getCode());
        }
        if (StringUtils.isNotBlank(extend.getServeList())){
            String[] serveIds = extend.getServeList().split(",");
            List<BizServeInfo> serveInfos =serveInfoService.queryByIds(serveIds);
            List<JSONObject> serveInfoVos = Lists.newArrayList();
            serveInfoVos.addAll(serveInfos.stream()
                    .map(serveInfo -> new JSONObject().fluentPut("serveId", serveInfo.getId()).fluentPut("serveName", serveInfo.getName()))
                    .toList());
            vo.setServeInfoVos(serveInfoVos);
        }
        return vo;
    }
}
