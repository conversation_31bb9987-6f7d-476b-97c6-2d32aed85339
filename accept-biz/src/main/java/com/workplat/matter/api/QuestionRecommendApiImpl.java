package com.workplat.matter.api;

import cn.hutool.core.bean.BeanUtil;
import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.common.core.util.CacheDictUtils;
import com.workplat.gss.log.annotation.ApiLogging;
import com.workplat.gss.log.constant.OperationType;
import com.workplat.matter.converter.BizQuestionRecommendConverter;
import com.workplat.matter.entity.BizQuestionRecommend;
import com.workplat.matter.service.BizQuestionRecommendService;
import com.workplat.matter.vo.QuestionRecommendDTO;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> cheng
 * @package com.workplat.matter.api
 * @description
 * @date 2025/5/27 13:24
 */
@RestController
public class QuestionRecommendApiImpl implements QuestionRecommendApi{

    @Autowired
    private BizQuestionRecommendService recommendService;
    @Autowired
    private BizQuestionRecommendConverter recommendConverter;

    @Override
    @ApiLogging(module = "边聊边办问题推荐模块", operation = "保存推荐", type = OperationType.INSERT)
    public ResponseData<QuestionRecommendDTO> save(QuestionRecommendDTO dto) {
        BizQuestionRecommend recommend = new  BizQuestionRecommend();
        if (StringUtils.isNotBlank(dto.getId())){
            BizQuestionRecommend old = recommendService.queryById(dto.getId());
            if (old != null){
                recommend = old;
            }
        }
        recommend.setName(dto.getName());
        recommend.setType(dto.getType());
        recommendService.save(recommend);
        return ResponseData.success("保存成功", dto);
    }

    @Override
    @ApiLogging(module = "边聊边办问题推荐模块", operation = "获取推荐列表", type = OperationType.QUERY)
    public ResponseData<Page<QuestionRecommendDTO>> page(PageableDTO pageDTO, String type) {
        HashMap<String, Object> map = new HashMap<>();
        if (StringUtil.isNotBlank(type)) {
            map.put("=(type)", type);
        }
        Page<BizQuestionRecommend> page = recommendService.queryForPage(map, pageDTO.convertPageable());
        return ResponseData.success(recommendConverter.convert(page));
    }

    @Override
    @ApiLogging(module = "边聊边办问题推荐模块", operation = "删除推荐", type = OperationType.DELETE)
    public ResponseData<Void> delete(String id) {
        recommendService.deleteById(id);
        return ResponseData.success().build();
    }

    @Override
    @ApiLogging(module = "边聊边办问题推荐模块", operation = "通过id获取推荐", type = OperationType.QUERY)
    public ResponseData<QuestionRecommendDTO> getById(String id) {
        BizQuestionRecommend recommend = recommendService.queryById(id);
        if (recommend != null) {
            return ResponseData.success(recommendConverter.convert(recommend));
        }
        return ResponseData.success().build();
    }

    @Override
    @ApiLogging(module = "边聊边办问题推荐模块", operation = "问题推荐清单", type = OperationType.QUERY)
    public ResponseData<Map<String, List<String>>> questionList() {
        List<BizQuestionRecommend> list = recommendService.queryForAll();
        Map<String, String> dictMap = CacheDictUtils.getDictMapByCode("question_type");
        // List<String>内容无序展示
        Map<String, List<String>> map = list.stream()
                .collect(Collectors.groupingBy(item -> dictMap.get(item.getType()),
                        Collectors.mapping(BizQuestionRecommend::getName,
                                Collectors.collectingAndThen(Collectors.toList(), lst -> {
                                    java.util.Collections.shuffle(lst);
                                    return lst;
                                }))));        return ResponseData.success(map);
    }

}

