package com.workplat.matter.entity;

import com.workplat.gss.common.core.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

/**
 * <AUTHOR> cheng
 * @package com.workplat.matter.entity
 * @description 网点窗口信息
 * @date 2025/6/10 13:22
 */
@Setter
@Getter
@Entity
@Table(name = "biz_network_window")
public class BizNetworkWindow extends BaseEntity {

    @Comment("网点名称")
    @Column(name = "network_name", length = 50)
    private String networkName;

    @Comment("编码")
    @Column(name = "code", length = 50)
    private String code;

    @Comment("窗口信息")
    @Column(name = "window_info", length = 50)
    private String windowInfo;

    @Comment("网点经度")
    @Column(name = "longitude", length = 50)
    private String longitude;

    @Comment("网点纬度")
    @Column(name = "latitude", length = 50)
    private String latitude;
}
