package com.workplat.matter.entity;

import com.workplat.gss.common.core.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

/**
 * <AUTHOR> cheng
 * @package com.workplat.matter.entity
 * @description 信息已阅记录
 * @date 2025/6/9 14:13
 */
@Setter
@Getter
@Entity
@Table(name = "biz_read_confirm")
public class BizReadConfirm extends BaseEntity {

    @Comment("userId")
    @Column(name = "user_id", length = 50)
    private String userId;

    @Comment("阅读内容唯一标识")
    @Column(name = "read_code", length = 50)
    private String readCode;

    @Comment("是否已读")
    @Column(name = "is_read")
    private boolean read;
}
