package com.workplat.matter.entity;

import com.workplat.gss.common.core.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

/**
 * <AUTHOR> cheng
 * @package com.workplat.matter.entity
 * @description 授权操作
 * @date 2025/6/6 13:17
 */
@Setter
@Getter
@Entity
@Table(name = "biz_auth_operation")
public class BizAuthOperation extends BaseEntity {

    @Comment("授权人姓名")
    @Column(name = "name", length = 50)
    private String name;

    @Comment("授权人手机号")
    @Column(name = "phone", length = 50)
    private String phone;

    @Comment("授权人身份证号")
    @Column(name = "idCard", length = 50)
    private String idCard;

    @Comment("实例id")
    @Column(name = "instance_id", length = 32)
    private String instanceId;

    @Comment("授权类型")
    @Column(name = "type", length = 50)
    private String type;

    @Comment("是否人脸核验")
    @Column(name = "is_face", columnDefinition = "int default 0")
    private boolean face;

    @Comment("签名文件地址")
    @Column(name = "sign_file_url")
    private String signFileUrl;
}
