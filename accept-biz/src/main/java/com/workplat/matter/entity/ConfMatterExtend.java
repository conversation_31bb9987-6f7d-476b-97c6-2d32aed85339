package com.workplat.matter.entity;

import com.workplat.accept.business.serve.entity.BizServeInfo;
import com.workplat.flow.entity.ConfFlow;
import com.workplat.gss.common.core.entity.BaseEntity;
import com.workplat.gss.service.item.dubbo.matter.entity.ConfMatter;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

import java.util.List;

/**
 * <AUTHOR> cheng
 * @package com.workplat.matter.entity
 * @description 事项扩展信息表
 * @date 2025/5/20 13:50
 */
@Setter
@Getter
@Entity
@Table(name = "conf_matter_extend")
public class ConfMatterExtend extends BaseEntity {

    @OneToOne
    @JoinColumn(name = "matter_id")
    @Comment("事项信息")
    private ConfMatter matter;

    @ManyToOne
    @JoinColumn(name = "conf_flow_id")
    @Comment("流程信息")
    private ConfFlow confFlow;

    @ManyToOne
    @JoinColumn(name = "pc_conf_flow_id")
    @Comment("pc流程信息")
    private ConfFlow pcConfFlow;

    @Comment("信息提交后的告知内容")
    @Column(name = "inform_after_submit")
    private String informAfterSubmit;

    /**
     * 多个服务用，拼接
     */
    @Comment("关联服务列表")
    @Column(name = "serve_List")
    private String serveList;

    @Comment("知情同意内容")
    @Lob
    @Column(name = "informed_consent", columnDefinition = "LONGTEXT")
    private String informedConsent;

    @Comment("申报须知（第三方）提示内容")
    @Lob
    @Column(name = "instruction_remote_tip", columnDefinition = "LONGTEXT")
    private String instructionRemoteTip;


}
