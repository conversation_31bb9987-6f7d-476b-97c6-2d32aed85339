package com.workplat.matter.service.impl;

import com.workplat.gss.common.core.service.impl.BaseServiceImpl;
import com.workplat.matter.entity.BizQuestionRecommend;
import com.workplat.matter.service.BizQuestionRecommendService;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR> cheng
 * @package com.workplat.matter.service.impl
 * @description 问题推荐serviceImpl
 * @date 2025/5/26 16:44
 */
@Service
public class BizQuestionRecommendServiceImpl extends BaseServiceImpl<BizQuestionRecommend> implements BizQuestionRecommendService {
    @Override
    public Map<String, String[]> questionList() {
        return Map.of();
    }
}
