package com.workplat.conf.component.entity;

import com.workplat.gss.common.core.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Comment;

@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "conf_component")
@Getter
@Setter
public class ConfComponent extends BaseEntity {

    @Comment("名称")
    @Column(name = "name", length = 32)
    private String name;

    @Comment("编码")
    @Column(name = "code", length = 32)
    private String code;

    @Comment("类型")
    @Column(name = "type", length = 32)
    private String type;

    @Comment("描述")
    @Column(name = "description")
    private String description;

    @Comment("内容")
    @Lob
    @Column(name = "content", columnDefinition = "LONGTEXT")
    private String content;

    @Comment("引擎编码")
    @Column(name = "engine_code", length = 32)
    private String engineCode;
}
