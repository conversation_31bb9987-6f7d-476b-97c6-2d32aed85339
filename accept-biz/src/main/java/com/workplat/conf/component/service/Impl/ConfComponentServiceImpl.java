package com.workplat.conf.component.service.Impl;

import cn.hutool.core.map.MapUtil;
import com.workplat.conf.component.entity.ConfComponent;
import com.workplat.conf.component.service.ConfComponentService;
import com.workplat.flow.service.ConfFlowComponentService;
import com.workplat.flow.service.ConfFlowService;
import com.workplat.gss.common.core.exception.BusinessException;
import com.workplat.gss.common.core.service.impl.BaseServiceImpl;
import com.workplat.gss.common.core.util.EntityValidateUtils;
import com.workplat.gss.service.item.dubbo.matter.service.ConfMatterService;
import com.workplat.matter.service.ConfMatterExtendService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ConfComponentServiceImpl extends BaseServiceImpl<ConfComponent> implements ConfComponentService {

    @Autowired
    ConfFlowService confFlowService;
    @Autowired
    ConfFlowComponentService confFlowComponentService;
    @Autowired
    ConfMatterService  confMatterService;
    @Autowired
    ConfMatterExtendService confMatterExtendService;

    public void entityValidate(ConfComponent entity) {
        if (EntityValidateUtils.duplicate(this, entity.getId(), "code", entity.getCode())) {
            throw new BusinessException("组件编码：" + entity.getCode() + "已存在");
        }
    }

    @Override
    public ConfComponent getByCode(String code) {
        return queryForSingle(MapUtil.<String, Object>builder().put("=(code)", code).build());
    }
}
