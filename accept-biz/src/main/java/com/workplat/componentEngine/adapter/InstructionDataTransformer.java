package com.workplat.componentEngine.adapter;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 指令数据转换器
 * 专门处理fillData方法中的数据转换逻辑
 * 
 * <AUTHOR>
 * @date 2025/06/25
 */
@Slf4j
@Component
public class InstructionDataTransformer {
    
    /**
     * 转换API响应数据为表单数据格式
     * 
     * @param responseBody API响应体
     * @return 转换后的数据，包含data和codeList
     */
    public JSONObject transformApiResponse(String responseBody) {
        if (responseBody == null) {
            return createEmptyResult();
        }
        
        try {
            JSONObject responseJson = JSON.parseObject(responseBody);
            List<Map<String, Object>> dataList = responseJson.getObject("data", List.class);
            
            if (dataList == null || dataList.isEmpty()) {
                return createEmptyResult();
            }
            
            return transform(dataList);
        } catch (Exception e) {
            log.error("API响应数据转换失败: responseBody={}, error={}", responseBody, e.getMessage());
            return createEmptyResult();
        }
    }
    
    /**
     * 转换数据列表为JSON对象
     * 
     * @param dataList 数据列表
     * @return 包含data和codeList的JSON对象
     */
    public JSONObject transform(List<Map<String, Object>> dataList) {
        JSONObject result = new JSONObject();
        JSONObject codeValueMap = new JSONObject();
        Set<String> codeList = new HashSet<>();
        
        for (Map<String, Object> item : dataList) {
            String code = (String) item.get("code");
            Object value = item.get("value");
            
            if (value instanceof List<?> listValue) {
                handleListValue(code, listValue, codeValueMap, codeList);
            } else {
                handleSimpleValue(code, value, codeValueMap, codeList);
            }
        }
        
        result.put("data", codeValueMap);
        result.put("codeList", codeList);
        return result;
    }
    
    /**
     * 处理列表类型的值
     */
    private void handleListValue(String code, List<?> listValue, JSONObject codeValueMap, Set<String> codeList) {
        if (listValue.isEmpty()) {
            return;
        }
        
        // 判断是否是嵌套列表（比如 eiAdjunctBeans 的 value 是 List<List<...>>）
        if (listValue.getFirst() instanceof List) {
            List<JSONObject> transformedList = new ArrayList<>();
            for (Object subListObj : listValue) {
                List<?> subList = (List<?>) subListObj;
                JSONObject obj = new JSONObject();
                convertListToObject(subList, obj, codeList);
                transformedList.add(obj);
            }
            codeValueMap.put(code, transformedList);
        } else {
            // 处理类似 eiContractDataBean 的情况
            convertListToObject(listValue, codeValueMap, codeList);
        }
    }
    
    /**
     * 处理简单类型的值
     */
    private void handleSimpleValue(String code, Object value, JSONObject codeValueMap, Set<String> codeList) {
        if (value != null) {
            codeValueMap.put(code, value.toString());
            codeList.add(code);
        }
    }
    
    /**
     * 将列表转换为对象
     */
    private void convertListToObject(List<?> subList, JSONObject obj, Set<String> codeList) {
        for (Object subItem : subList) {
            if (subItem instanceof Map<?, ?> subMap) {
                String subCode = (String) subMap.get("code");
                Object subValue = subMap.get("value");
                if (subValue != null) {
                    obj.put(subCode, subValue.toString());
                    codeList.add(subCode);
                }
            }
        }
    }
    
    /**
     * 创建空结果
     */
    private JSONObject createEmptyResult() {
        JSONObject result = new JSONObject();
        result.put("data", new JSONObject());
        result.put("codeList", new HashSet<>());
        return result;
    }
}
