package com.workplat.componentEngine.adapter;

import com.alibaba.fastjson2.JSONObject;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;

/**
 * 远程数据适配器接口
 * 用于解耦远程数据获取和转换的具体实现
 * 
 * <AUTHOR>
 * @date 2025/06/25
 */
public interface RemoteDataAdapter {
    
    /**
     * 获取远程数据
     * 
     * @param url 请求URL
     * @param method HTTP方法
     * @param headers 请求头
     * @param requestBody 请求体（可选）
     * @return 响应数据
     */
    String fetchData(String url, HttpMethod method, HttpHeaders headers, String requestBody);
    
    /**
     * 转换响应数据为组件所需格式
     * 
     * @param responseBody 原始响应数据
     * @return 转换后的数据
     */
    Object transformData(String responseBody);
    
    /**
     * 获取配置信息
     * 
     * @param content 组件配置内容
     * @param configKey 配置键名（如"init"、"buttons.detail"）
     * @return 配置对象
     */
    JSONObject getConfig(String content, String configKey);
}
