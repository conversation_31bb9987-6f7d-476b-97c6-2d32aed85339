package com.workplat.componentEngine.engine;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeType;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.text.SimpleDateFormat;
import java.util.Iterator;
import java.util.Map;

public class JsonSchemaGenerator {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static JsonNode convertToExampleSchema(String actualResponse) throws Exception {
        JsonNode rootNode = objectMapper.readTree(actualResponse);
        return buildSchema(rootNode);
    }

    private static JsonNode buildSchema(JsonNode node) {
        switch (node.getNodeType()) {
            case OBJECT:
                ObjectNode objectNode = objectMapper.createObjectNode();
                Iterator<Map.Entry<String, JsonNode>> fields = node.fields();
                while (fields.hasNext()) {
                    Map.Entry<String, JsonNode> field = fields.next();
                    objectNode.set(field.getKey(), buildSchema(field.getValue()));
                }
                return objectNode;
            case ARRAY:
                ArrayNode arrayNode = objectMapper.createArrayNode();
                if (node.isEmpty()) {
                    arrayNode.add("unknown");
                } else {
                    JsonNode firstElement = node.get(0);
                    arrayNode.add(buildSchema(firstElement));
                }
                return arrayNode;
            default:
                return objectMapperTextNode(inferTypeDetailed(node));
        }
    }

    private static String inferTypeDetailed(JsonNode node) {
        if (node.isNull()) {
            return "null"; // You can decide if you want this to be a specific value or type
        } else if (node.isTextual()) {
            String text = node.asText();
            // Check for integer
            if (text.matches("^\\d+$")) {
                return "integer";
            }
            // Check for number with decimal points
            else if (text.matches("^\\d+(\\.\\d+)?$")) {
                return "number";
            }
            // Check for date format (ISO 8601 date format)
            else if (text.matches("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d+)?Z?$")) {
                return "datetime";
            } else {
                return "string";
            }
        } else if (node.isBoolean()) {
            return "boolean";
        } else if (node.isInt()) {
            return "integer";
        } else if (node.isDouble()) {
            return "number";
        } else if (node.isArray()) {
            return "array";
        } else if (node.isObject()) {
            return "object";
        } else {
            return "unknown";
        }
    }

    private static JsonNode objectMapperTextNode(String value) {
        return objectMapper.getNodeFactory().textNode(value);
    }

    public static void main(String[] args) throws Exception {
        String actualResponse = "{\"message\":null,\"error\":null,\"path\":null,\"status\":200,\"timestamp\":1737601815759,\"data\":[{\"areaName\":\"太仓市\",\"areaCode\":\"320585\",\"areaLevel\":\"4\",\"basicId\":null,\"enable\":false,\"taskCode\":\"000117015000\",\"areas\":null},{\"areaName\":\"璜泾镇便民服务中心\",\"areaCode\":\"320585HJ\",\"areaLevel\":\"5\",\"basicId\":null,\"enable\":false,\"taskCode\":\"000117015000\",\"areas\":null},{\"areaName\":\"沙溪镇便民服务中心\",\"areaCode\":\"320585SX\",\"areaLevel\":\"5\",\"basicId\":null,\"enable\":false,\"taskCode\":\"000117015000\",\"areas\":null},{\"areaName\":\"城厢镇便民服务中心\",\"areaCode\":\"320585CXZ\",\"areaLevel\":\"5\",\"basicId\":null,\"enable\":false,\"taskCode\":\"000117015000\",\"areas\":null},{\"areaName\":\"浏河镇便民服务中心\",\"areaCode\":\"320585LH\",\"areaLevel\":\"5\",\"basicId\":null,\"enable\":false,\"taskCode\":\"000117015000\",\"areas\":null},{\"areaName\":\"双凤镇便民服务中心\",\"areaCode\":\"320585SFZ\",\"areaLevel\":\"5\",\"basicId\":null,\"enable\":false,\"taskCode\":\"000117015000\",\"areas\":null},{\"areaName\":\"太仓港便民服务中心\",\"areaCode\":\"320585GQ\",\"areaLevel\":\"5\",\"basicId\":null,\"enable\":false,\"taskCode\":\"000117015000\",\"areas\":null}],\"isSuccess\":true}";

        JsonNode exampleSchema = convertToExampleSchema(actualResponse);
        System.out.println(exampleSchema.toPrettyString());
    }
}
