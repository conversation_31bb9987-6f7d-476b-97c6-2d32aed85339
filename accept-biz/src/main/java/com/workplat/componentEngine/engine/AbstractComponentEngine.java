package com.workplat.componentEngine.engine;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.workplat.accept.business.chat.dto.ChatProcessDTO;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.engine.content.InstructionConstant;
import com.workplat.conf.component.entity.ConfComponent;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.componentEngine.service.ComponentEngineInterface;
import com.workplat.utils.ChatCacheUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class AbstractComponentEngine implements ComponentEngineInterface {

    protected ComponentDataContext componentDataContext;

    @Autowired
    protected ChatCacheUtil chatCacheUtil;

    // 抽象方法：由子类实现具体的业务逻辑
    protected abstract ComponentRunVO doExecute();

    // 公共入口方法：统一处理上下文设置和充实
    public final ComponentRunVO execute(ComponentDataContext context) {
        this.componentDataContext = context;
        // 获取缓存的流程信息, 用于获取实例ID
        if (StringUtils.isBlank(componentDataContext.getInstanceId())) {
            ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentDataContext.getRecordId());
            componentDataContext.setInstanceId(chatProcessDTO.getInstanceId());
        }
        enrichContext();

        ComponentRunVO doneExecute = doExecute();
        // 设置基本属性
        setBasicProperties(doneExecute, componentDataContext.getConfComponent());
        return doneExecute;
    }

    /**
     * 设置组件运行结果的基本属性
     */
    protected void setBasicProperties(ComponentRunVO result, ConfComponent confComponent) {
        if (result != null && confComponent != null) {
            // 设置实例ID
            result.setInstanceId(componentDataContext.getInstanceId());
            // 设置组件基本信息
            result.setComponentCode(confComponent.getCode());
            result.setMetadata(confComponent.getContent());
            result.setEngineCode(confComponent.getEngineCode());
        }
    }

    @Override
    public void fillData(ComponentDataContext componentDataContext) {
    }

    // 充实上下文内容，子类可以覆盖此方法以提供特定的上下文充实逻辑
    protected void enrichContext() {
        // 默认实现不做任何操作
    }

    // 示例方法：模拟接口调用
    private String fetchEnrichedDataFromApi() {
        return "Enriched Data from API";
    }


    /**
     * 由子类实现的具体判断逻辑
     */
    public abstract boolean canHandle(ComponentDataContext context);

    @Override
    public String getNextInstruction(ComponentDataContext componentDataContext) {
        // 默认返回成功，子类可以覆盖此方法以提供自定义的返回逻辑
        return InstructionConstant.SUCCESS_END.getCode();
    }

    public String getConfigValue(ComponentDataContext context, String key) {
        String content = context.getConfComponent().getContent();
        JSONObject config = JSON.parseObject(content);
        if (config != null) {
            return config.getString(key);
        }
        return null;
    }
}
