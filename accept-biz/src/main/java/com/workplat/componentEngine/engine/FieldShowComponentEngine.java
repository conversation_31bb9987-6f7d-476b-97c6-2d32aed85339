package com.workplat.componentEngine.engine;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.workplat.accept.business.chat.dto.ChatProcessDTO;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.gss.application.biz.converter.BizInstanceFieldsConvert;
import com.workplat.gss.application.dubbo.entity.BizInstanceFields;
import com.workplat.gss.application.dubbo.service.BizInstanceFieldsService;
import com.workplat.gss.application.dubbo.vo.BizInstanceFieldsVO;
import com.workplat.utils.FormFieldFilterUtil;
import com.workplat.utils.FormStepProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 表单提取展示组件
 *
 * <AUTHOR>
 * @date 2025/06/20
 */
@Slf4j
@Service
public class FieldShowComponentEngine extends AbstractComponentEngine {

    private final BizInstanceFieldsService bizInstanceFieldsService;
    private final BizInstanceFieldsConvert bizInstanceFieldsConvert;
    private static final String CODE = "FieldExtraction";

    public FieldShowComponentEngine(BizInstanceFieldsService bizInstanceFieldsService,
                                    BizInstanceFieldsConvert bizInstanceFieldsConvert) {
        this.bizInstanceFieldsService = bizInstanceFieldsService;
        this.bizInstanceFieldsConvert = bizInstanceFieldsConvert;
    }

    @Override
    protected ComponentRunVO doExecute() {
        log.info("FieldShowComponentEngine execute");
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentDataContext.getRecordId());


        // 获取表单信息
        BizInstanceFields bizInstanceFields = bizInstanceFieldsService.
                queryForSingle(MapUtil.<String, Object>builder().put("=(instance.id)", componentDataContext.getInstanceId()).build());
        BizInstanceFieldsVO bizInstanceFieldsVO = new BizInstanceFieldsVO();
        bizInstanceFieldsConvert.convert(bizInstanceFields, bizInstanceFieldsVO);
        // 获取AI提取字段
        Map<String, Object> aiExtractFields = chatProcessDTO.getAiExtractFields();

        // 处理 formJson - 使用增强版过滤方法，自动处理空表单域
        try {
            FormFieldFilterUtil.FormFilterResult filterResult = FormFieldFilterUtil.filterFormWithIndexTracking(
                    bizInstanceFieldsVO.getFormJson(),
                    JSON.toJSONString(aiExtractFields),
                    true // 保留模式
            );

            String formJson = filterResult.getFilteredFormJson();
            bizInstanceFieldsVO.setFormJson(formJson);

            // 记录被移除的空表单域信息（用于日志和调试）
            if (!filterResult.getRemovedFormAreaIndexes().isEmpty()) {
                log.info("字段展示组件移除了空表单域，索引: {}, 字段: {}",
                        filterResult.getRemovedFormAreaIndexes(),
                        filterResult.getRemovedFormAreaFields());
            }
        } catch (Exception e) {
            log.error("表单提取展示组件执行提取表单元数据结构异常", e);
            bizInstanceFieldsVO.setFormJson(bizInstanceFieldsVO.getFormJson());
        }

        // 输出结果
        String formObj = bizInstanceFieldsVO.getFormObj();
        if (formObj != null) {
            @SuppressWarnings("unchecked")
            Map<String, Object> formObjMap = JSON.parseObject(formObj, Map.class);
            formObjMap.putAll(aiExtractFields);
            bizInstanceFieldsVO.setFormObj(JSON.toJSONString(formObjMap));
        } else {
            bizInstanceFieldsVO.setFormObj(JSON.toJSONString(aiExtractFields));
        }

        // 组装结果
        ComponentRunVO.RenderData fieldFormRenderData = ComponentRunVO.RenderData.builder()
                .componentName(CODE)
                .componentInfo(bizInstanceFieldsVO)
                .build();

        ComponentRunVO vo = new ComponentRunVO();
        List<ComponentRunVO.RenderData> renderDataList =
                Collections.singletonList(fieldFormRenderData);
        vo.setRenderData(renderDataList);
        vo.setTips(getConfigValue(componentDataContext,"tips"));
        return vo;
    }

    @Override
    public boolean canHandle(ComponentDataContext context) {
        return CODE.equals(context.getConfComponent().getEngineCode());
    }

    @Override
    public void fillData(ComponentDataContext componentDataContext) {
        Object submitData = componentDataContext.getSubmitData();
        log.info("submitData:{}", JSON.toJSONString(submitData));
        @SuppressWarnings("unchecked")
        Map<String, Object> submitDataMap = JSON.parseObject(submitData.toString(), Map.class);
        if (submitDataMap != null) {
            BizInstanceFields bizInstanceFields = bizInstanceFieldsService.
                    queryForSingle(MapUtil.<String, Object>builder().put("=(instance.id)", componentDataContext.getInstanceId()).build());
            String formObj = bizInstanceFields.getFormObj();
            if (formObj != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> formObjMap = JSON.parseObject(formObj, Map.class);
                formObjMap.putAll(submitDataMap);
                bizInstanceFields.setFormObj(JSON.toJSONString(formObjMap));
            } else {
                bizInstanceFields.setFormObj(JSON.toJSONString(submitDataMap));
            }
            bizInstanceFieldsService.update(bizInstanceFields);
        }
    }
}
