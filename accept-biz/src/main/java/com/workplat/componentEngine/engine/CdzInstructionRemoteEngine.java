package com.workplat.componentEngine.engine;

import com.google.common.collect.ImmutableMap;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.adapter.InstructionDataTransformer;
import com.workplat.componentEngine.adapter.RemoteDataAdapter;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.entity.BizInstanceQuota;
import com.workplat.gss.application.dubbo.service.BizInstanceFieldsService;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.service.BizInstanceQuotaService;
import com.workplat.matter.service.ConfMatterExtendService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> cheng
 * @package com.workplat.componentEngine.engine
 * @description 申报须知（第三方接口获取）组件引擎
 * @date 2025/5/28 14:41
 */
@Slf4j
@Service
public class CdzInstructionRemoteEngine extends AbstractInstructionRemoteEngine {

    private final String CODE = "CdzInstruction";
    private final String NAME = "申报须知";
    private final BizInstanceInfoService bizInstanceInfoService;
    private final BizInstanceQuotaService bizInstanceQuotaService;

    public CdzInstructionRemoteEngine(@Qualifier("cdzRemoteDataAdapter") RemoteDataAdapter remoteDataAdapter,
                                      InstructionDataTransformer dataTransformer,
                                      BizInstanceFieldsService bizInstanceFieldsService,
                                      BizInstanceInfoService bizInstanceInfoService,
                                      ConfMatterExtendService confMatterExtendService,
                                      BizInstanceQuotaService bizInstanceQuotaService) {
        super(remoteDataAdapter, dataTransformer, bizInstanceFieldsService, bizInstanceInfoService, confMatterExtendService);
        this.bizInstanceInfoService = bizInstanceInfoService;
        this.bizInstanceQuotaService = bizInstanceQuotaService;
    }

    @Override
    protected ComponentRunVO doExecute() {
        BizInstanceQuota quota = bizInstanceQuotaService
                .queryForSingle(ImmutableMap.of("=(instance.id)", componentDataContext.getInstanceId(), "=(title)", "车位性质"));
        if (!quota.getOptions().isEmpty() && "租赁车位".equals(quota.getOptions().getFirst().getLabel())) {
            BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(componentDataContext.getInstanceId());
            InstructionRemoteData instructionRemoteData = getInstructionRemoteData(bizInstanceInfo);
            return getComponentRunVO(instructionRemoteData);
        }
        // 默认处理
        return process();
    }

    @Override
    public boolean canHandle(ComponentDataContext context) {
        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(context.getInstanceId());
        return super.canHandle(context) && "cdzbgyjs".equals(bizInstanceInfo.getMatterCode());
    }
}

