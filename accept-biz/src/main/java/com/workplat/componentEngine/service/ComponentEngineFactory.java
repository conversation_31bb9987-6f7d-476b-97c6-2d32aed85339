package com.workplat.componentEngine.service;

import com.workplat.componentEngine.dto.ComponentRunDTO;
import com.workplat.conf.component.entity.ConfComponent;
import com.workplat.conf.component.service.ConfComponentService;
import com.workplat.componentEngine.engine.AbstractComponentEngine;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
public class ComponentEngineFactory {

    private final List<AbstractComponentEngine> allEngines;
    private final ConfComponentService confComponentService;

    public ComponentEngineFactory(List<AbstractComponentEngine> componentEngines,
                                  ConfComponentService confComponentService) {
        this.allEngines = componentEngines;
        this.confComponentService = confComponentService;
    }

    /**
     * 根据上下文获取对应的组件引擎
     */
    public Optional<ComponentEngineInterface> getEngine(ComponentDataContext context) {
        // 只要遍历所有引擎，依赖canHandle方法的高效实现
        for (AbstractComponentEngine engine : allEngines) {
            if (engine.canHandle(context)) {
                return Optional.of(engine);
            }
        }

        return Optional.empty();
    }

    public Object run(ComponentRunDTO componentRunDto) {
        ComponentDataContext componentDataContext = getComponentDataContext(componentRunDto);
        return getEngine(componentDataContext)
                .map(engine -> engine.execute(componentDataContext))
                .orElse(null);
    }

    @NotNull
    private ComponentDataContext getComponentDataContext(ComponentRunDTO componentRunDto) {
        ConfComponent component = confComponentService.getByCode(componentRunDto.getComponentCode());
        ComponentDataContext componentDataContext = new ComponentDataContext();
        componentDataContext.setConfComponent(component);
        componentDataContext.setRecordId(componentRunDto.getRecordId());
        componentDataContext.setInstanceId(componentRunDto.getInstanceId());
        componentDataContext.setSubmitData(componentRunDto.getSubmitData());
        return componentDataContext;
    }

    public void backFillData(ComponentRunDTO componentRunDto) {
        ComponentDataContext componentDataContext = getComponentDataContext(componentRunDto);
        getEngine(componentDataContext)
                .ifPresent(engine -> engine.fillData(componentDataContext));
    }

    public Object getNextInstruction(ComponentRunDTO componentRunDto) {
        ComponentDataContext componentDataContext = getComponentDataContext(componentRunDto);
        return getEngine(componentDataContext)
                .map(engine -> engine.getNextInstruction(componentDataContext))
                .orElse(null);
    }
}