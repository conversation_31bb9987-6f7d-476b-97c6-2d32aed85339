package com.workplat.electronic.certificate;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.workplat.gss.common.core.util.Sm4Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;

/**
 * todo 电子证照获取
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/10 10:06
 */
@Service
@Slf4j
public class CertificateRequestService {

    public static final String URL = "http://2.35.100.150/certificate-test";
    public static final String licenseCode = "WS_0090";

    public String findCertificateByHolderCodeAndTypeCodes() {
        String body = HttpRequest.post(URL + "/api/safety/certificate/custom/findCertificateByHolderCodeAndTypeCodes")
                .body(new JSONObject()
                        .fluentPut("holderType", "PERSON")
                        .fluentPut("certificateHolderCode", Sm4Util.encodeStr("320522199208160021", "Td4EegzAIWygYpx8"))
                        .fluentPut("certificateTypeCodes", ListUtil.of(licenseCode))
                        .fluentPut("platformCode", "test")
                        .fluentPut("usageIp", "127.0.0.1")
                        .fluentPut("serviceItemCode", "test")
                        .fluentPut("serviceItemName", "测试证照")
                        .fluentPut("queryPersonName", Sm4Util.encodeStr("陆燚恺", "Td4EegzAIWygYpx8"))
                        .fluentPut("queryPersonCardId", Sm4Util.encodeStr("320522199208160021", "Td4EegzAIWygYpx8"))
                        .toJSONString())
                .execute()
                .body();
        return body;
    }

    public byte[] downloadCertificate(String body) {
                String certFileId = JSONArray.parseArray(body).getJSONObject(0).getString("previewCertFileId");
//        String certFileId = JSONArray.parseArray(body).getJSONObject(0).getString("certFileId");
        byte[] bytes = HttpRequest.post(URL + "/api/safety/certificate/custom/downloadCertificate")
                .body(new JSONObject()
                        .fluentPut("platformCode", "test")
                        .fluentPut("serviceItemCode", "test")
                        .fluentPut("serviceItemName", "测试证照")
                        .fluentPut("usageIp", "127.0.0.1")
                        .fluentPut("certFileId", certFileId)
                        .toJSONString()
                )
                .execute()
                .bodyBytes();
        FileUtil.writeBytes(bytes, new File("D:\\" + licenseCode + ".pdf"));
        return bytes;
    }
}
