package com.workplat.serve.importData;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * excel 校验工具
 *
 * @param <T> the type parameter
 * <AUTHOR>
 * @since 2024/10/17 16:39
 */
public class ExcelValidator<T> {

    private static final Validator validator;

    private final Integer beginIndex;

    static {
        try (ValidatorFactory validatorFactory = Validation.buildDefaultValidatorFactory()) {
            validator = validatorFactory.getValidator();
        }
    }

    public ExcelValidator(Integer beginIndex) {
        this.beginIndex = beginIndex;
    }


    /**
     * 集合校验
     *
     * @param data 待校验的集合
     * @return list
     */
    public String validate(Collection<T> data) {
        int index = beginIndex + 1;
        for (T datum : data) {
            String validated = this.doValidate(index, datum);
            if (StringUtils.hasText(validated)) {
                return validated;
            }
            index++;
        }
        return null;
    }

    /**
     * 这里是校验的根本方法
     *
     * @param index 本条数据所在的行号
     * @param data  待校验的某条数据
     * @return 对数据的校验异常进行提示，如果有触发校验规则的会封装提示信息。
     */
    private String doValidate(int index, T data) {
        // 这里使用了JSR303的的校验器，同时使用了分组校验，Excel为分组标识
        Set<ConstraintViolation<T>> validate = validator.validate(data);
        return !validate.isEmpty() ? "第" + index +
                "行，触发约束：" + validate.stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(",")) : "";
    }


    public String validate(T data) {
        int index = beginIndex + 1;
        String validated = this.doValidate(index, data);
        if (StringUtils.hasText(validated)) {
            return validated;
        }
        return null;
    }
}