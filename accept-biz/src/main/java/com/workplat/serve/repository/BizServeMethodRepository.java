package com.workplat.serve.repository;

import com.workplat.accept.business.serve.entity.BizServeMethod;
import com.workplat.gss.common.core.repository.BaseRepository;
import org.springframework.stereotype.Repository;

/**
 * @author: q<PERSON> cheng
 * @package: com.workplat.serve.repository
 * @description: 服务办理方式Repository
 * @date: 2025/5/14 15:36
 */
@Repository
public interface BizServeMethodRepository extends BaseRepository<BizServeMethod> {




}
