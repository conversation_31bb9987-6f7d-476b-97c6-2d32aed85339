package com.workplat.serve.service.impl;

import com.workplat.accept.business.serve.entity.BizServeMethod;
import com.workplat.gss.common.core.service.impl.BaseServiceImpl;
import com.workplat.serve.service.BizServeMethodService;
import org.springframework.stereotype.Service;

/**
 * @author: q<PERSON> cheng
 * @package: com.workplat.serve.service.impl
 * @description: 服务办理方式ServiceImpl
 * @date: 2025/5/14 15:52
 */
@Service
public class BizServeMethodServiceImpl extends BaseServiceImpl<BizServeMethod> implements BizServeMethodService {



}
