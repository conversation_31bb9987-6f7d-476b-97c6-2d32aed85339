package com.workplat.serve.converter;

import com.workplat.accept.business.serve.entity.BizServeMethod;
import com.workplat.accept.business.serve.vo.BizServeMethodVo;
import com.workplat.gss.common.core.converter.BaseConverter;
import org.springframework.stereotype.Component;


@Component
public class BizServeMethodConverter implements BaseConverter<BizServeMethod, BizServeMethodVo> {

    @Override
    public BizServeMethodVo convert(BizServeMethod source) {
        if (source ==null){
            return null;
        }
        BizServeMethodVo bizServeMethodVo=new BizServeMethodVo();
        bizServeMethodVo.setId(source.getId());
        bizServeMethodVo.setIconId(source.getIconId());
        bizServeMethodVo.setDescription(source.getDescription());
        bizServeMethodVo.setContent(source.getContent());
        if (source.getServe() !=null){
            bizServeMethodVo.setServeInfoId(source.getServe().getId());
        }
        bizServeMethodVo.setType(source.getType());
        bizServeMethodVo.setTypeCode(source.getType());
        bizServeMethodVo.setCertificationLevel(source.getCertificationLevel());
        bizServeMethodVo.setSort(source.getSort());
        return bizServeMethodVo;
    }
}
