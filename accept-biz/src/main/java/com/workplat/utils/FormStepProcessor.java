package com.workplat.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

public class FormStepProcessor {

    /**
     * 处理 JSON 表单 获取表单域的数量
     *
     * @param formJson 原始表单 JSON 字符串
     * @return 表单域的数量
     */
    public static int getFormStepCount(String formJson) {
        JSONObject result = new JSONObject();
        JSONObject formJsonObj = JSON.parseObject(formJson);

        if (formJsonObj.containsKey("renderList")) {
            JSONArray renderList = formJsonObj.getJSONArray("renderList");
            return renderList.size();
        }
        return 0;
    }

    /**
     * 处理 JSON 表单并根据步骤编号返回特定的 renderList 项
     *
     * @param formJson 原始表单 JSON 字符串
     * @param step     步数 （0， 1， 2）
     * @return JSONObject，其中包含请求的步骤数据或空对象（如果步骤无效）
     */
    public static JSONObject getStepData(String formJson, int step) {
        // 步长减一，因为数组索引从0开始
        int stepIndex = step - 1;
        JSONObject result = new JSONObject();
        JSONObject formJsonObj = JSON.parseObject(formJson);

        if (formJsonObj.containsKey("renderList")) {
            JSONArray renderList = formJsonObj.getJSONArray("renderList");

            if (stepIndex >= 0 && stepIndex < renderList.size()) {
                // Get the specific form area for the step
                JSONObject formArea = renderList.getJSONObject(stepIndex);

                // Create a new result object with the same structure but only the selected step
                result.put("tableName", formJsonObj.getString("tableName"));
                result.put("formAttribute", formJsonObj.getJSONObject("formAttribute"));
                result.put("beforeFunction", formJsonObj.getString("beforeFunction"));
                result.put("submitFunction", formJsonObj.getString("submitFunction"));

                // Create a new renderList with just the selected form area
                JSONArray singleStepRenderList = new JSONArray();
                singleStepRenderList.add(formArea);
                result.put("renderList", singleStepRenderList);
            }
        }

        return result;
    }

    /**
     * 返回指定步长之前的所有步长的替代版本
     *
     * @param formJson 原始表单 JSON 字符串
     * @param step     要包含的最大步数 （0， 1， 2）
     * @return JSONObject 包含指定步骤之前的所有步骤
     */
    public static JSONObject getStepsUpTo(String formJson, int step) {
        JSONObject result = new JSONObject();
        JSONObject formJsonObj = JSON.parseObject(formJson);

        if (formJsonObj.containsKey("renderList")) {
            JSONArray renderList = formJsonObj.getJSONArray("renderList");
            JSONArray filteredRenderList = new JSONArray();

            // Add all steps up to the specified one
            for (int i = 0; i <= step && i < renderList.size(); i++) {
                filteredRenderList.add(renderList.getJSONObject(i));
            }

            // Build the result object
            result.put("tableName", formJsonObj.getString("tableName"));
            result.put("formAttribute", formJsonObj.getJSONObject("formAttribute"));
            result.put("beforeFunction", formJsonObj.getString("beforeFunction"));
            result.put("submitFunction", formJsonObj.getString("submitFunction"));
            result.put("renderList", filteredRenderList);
        }

        return result;
    }

    /**
     * 判断是否还有字段可以填写
     *
     * @param formJson        表单JSON字符串
     * @param fieldsFilterMapStr 字段过滤映射JSON字符串
     * @return boolean 是否还有字段可以填写 true 表示还有字段可以填写，false 表示没有字段可以填写
     */
    public static boolean hasFieldsToFill(String formJson, String fieldsFilterMapStr) {
        JSONObject formJsonObj = JSON.parseObject(formJson);
        @SuppressWarnings("unchecked")
        Map<String, String> fieldsFilterMap = JSON.parseObject(fieldsFilterMapStr, HashMap.class);
        if (formJsonObj == null || !formJsonObj.containsKey("renderList")) {
            return false;
        }

        JSONArray renderList = formJsonObj.getJSONArray("renderList");
        if (renderList == null || renderList.isEmpty()) {
            return false;
        }

        // 递归检查是否有可编辑字段
        for (int i = 0; i < renderList.size(); i++) {
            JSONObject formArea = renderList.getJSONObject(i);
            if (hasFieldsInFormArea(formArea, fieldsFilterMap)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查表单域中是否有可填写的字段
     *
     * @param formArea           表单域对象
     * @param fieldsFilterMap    字段过滤映射
     * @return boolean 是否有可填写的字段
     */
    private static boolean hasFieldsInFormArea(JSONObject formArea, Map<String, String> fieldsFilterMap) {
        if (!formArea.containsKey("child")) {
            return false;
        }

        JSONArray children = formArea.getJSONArray("child");
        if (children == null || children.isEmpty()) {
            return false;
        }

        // 递归检查子组件
        for (int i = 0; i < children.size(); i++) {
            JSONObject child = children.getJSONObject(i);

            // 检查是否是可编辑的字段组件
            if (isEditableField(child, fieldsFilterMap)) {
                return true;
            }

            // 递归检查子组件的子元素
            if (hasFieldsInFormArea(child, fieldsFilterMap)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 判断组件是否是可编辑的字段
     *
     * @param component       组件对象
     * @param fieldsFilterMap 字段过滤映射
     * @return boolean 是否是可编辑字段
     */
    private static boolean isEditableField(JSONObject component, Map<String, String> fieldsFilterMap) {
        if (!component.containsKey("props")) {
            return false;
        }

        JSONObject props = component.getJSONObject("props");
        // 有field属性的组件认为是可编辑字段并且存在于过滤映射中
        return props.containsKey("field") && props.getString("field") != null && fieldsFilterMap.containsKey(props.getString("field"));
    }


    /**
     * 提取表单中的字段和标签映射
     * @param formJson 表单JSON字符串
     * @return 包含字段名和对应标签的映射
     */
    public static Map<String, String> extractFormFieldsMap(String formJson) {
        Map<String, String> fieldsMap = new HashMap<>();
        // 解析原始表单JSON
        JSONObject formJsonObj = JSON.parseObject(formJson);

        // 检查是否存在渲染列表
        if (formJsonObj.containsKey("renderList")) {
            JSONArray renderList = formJsonObj.getJSONArray("renderList");

            // 遍历每个表单区域
            for (int i = 0; i < renderList.size(); i++) {
                JSONObject formArea = renderList.getJSONObject(i);

                // 递归处理子组件并提取字段信息
                if (formArea.containsKey("child")) {
                    JSONArray children = formArea.getJSONArray("child");
                    fieldsMap.putAll(extractFieldsFromComponents(children));
                }
            }
        }
        return fieldsMap;
    }

    /**
     * 从组件数组中递归提取字段和标签
     * @param children 子组件数组
     * @return 字段和对应的标签映射
     */
    private static Map<String, String> extractFieldsFromComponents(JSONArray children) {
        Map<String, String> fieldsMap = new HashMap<>();
        
        for (int i = 0; i < children.size(); i++) {
            JSONObject child = children.getJSONObject(i);
            
            // 提取当前组件的字段和标签
            if (child.containsKey("props")) {
                JSONObject props = child.getJSONObject("props");
                String field = props.getString("field");
                String label = props.getString("label");
                
                if (field != null && label != null) {
                    fieldsMap.put(field, label);
                }
            }
            
            // 递归处理子组件的子元素
            if (child.containsKey("child") && !child.getJSONArray("child").isEmpty()) {
                JSONArray subChildren = child.getJSONArray("child");
                fieldsMap.putAll(extractFieldsFromComponents(subChildren));
            }
        }
        
        return fieldsMap;
    }

    public static void main(String[] args) throws IOException {
        String formPath = "accept-biz/src/main/java/com/workplat/utils/form.json";
//
//        String formMetadata = new String(Files.readAllBytes(Paths.get(formPath)));
//        Map<String, String> fieldsMap = extractFormFieldsMap(formMetadata);
//        System.out.println(fieldsMap);
        System.out.println(hasFieldsToFill("{\"tableName\":\"\",\"formAttribute\":{\"inline\":false,\"disabled\":false,\"tableName\":\"demoFormName\",\"labelWidth\":\"100px\",\"labelPosition\":\"right\"},\"beforeFunction\":\"{}\",\"submitFunction\":\"{}\",\"renderList\":[{\"id\":\"KfLjT25pj1\",\"icon\":\"icon-biaodan\",\"name\":\"表单域\",\"type\":\"formArea\",\"child\":[{\"id\":\"Y47o7mE9g\",\"icon\":\"icon-timeSelector\",\"name\":\"日期选择器\",\"child\":[],\"props\":{\"id\":\"1ae7cd18c56e46b189e8e1db5f8cb322\",\"key\":\"qfrq\",\"type\":\"date\",\"field\":\"qfrq\",\"label\":\"居住证签发日期\",\"format\":\"YYYY-MM-DD\",\"disabled\":false,\"readonly\":false,\"clearable\":false,\"functions\":[{\"key\":\"changeqfrq\",\"name\":\"change事件\",\"value\":\"\"}],\"labelWidth\":\"\",\"modelValue\":\"2025-06-25\",\"tableWidth\":\"\",\"isTimeLimit\":true,\"labelHeight\":\"\",\"placeholder\":\"请选择时间\",\"valueFormat\":\"YYYY-MM-DD\",\"labelDescribe\":\"\",\"endPlaceholder\":\"请选择结束时间\",\"rangeSeparator\":\"-\",\"controlsPosition\":\"\",\"startPlaceholder\":\"请选择开始时间\"},\"rules\":[{\"message\":\"居住证签发日期不能为空\",\"trigger\":\"blur\",\"required\":true}],\"events\":{},\"platform\":\"all\",\"needRules\":true,\"needSpecial\":true,\"specialName\":\"DataPickerAtribute\",\"componentName\":\"a-net-date-picker\"}],\"props\":{\"show\":true,\"field\":\"f2BgEPZ1Q\",\"title\":\"申请人信息\",\"bgColor\":\"#3A81FF\",\"barColor\":\"\",\"isHidden\":false,\"functions\":[],\"titleSize\":22,\"arrowColor\":\"#000000\",\"titleColor\":\"#0E0D0D\",\"isShowButton\":true},\"rules\":[],\"events\":{},\"platform\":\"all\",\"needSpecial\":false,\"componentName\":\"ANetFormArea\"}]}",
                "{\"blsy\": \"事由\", \"csrq\": \"出生日期\", \"fhrq\": \"返回日期\", \"qwdd\": \"前往地点\", \"qwrq\": \"前往日期\", \"yjdz\": \"邮寄地址\", \"sfzhm\": \"身份证号码\", \"sjrxm\": \"收件人姓名\", \"sqrxm\": \"申请人姓名\", \"sqrzz\": \"住址\", \"lqpzfs\": \"领取凭证方式\", \"sjrsjhm\": \"收件人手机号码\", \"sqrlxdh\": \"联系电话\", \"xingbie\": \"性别\"}"));
    }
}