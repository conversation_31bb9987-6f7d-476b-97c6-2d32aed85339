package com.workplat.utils;

import java.math.BigInteger;

/**
 * 分数和百分数工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/28 11:13
 */
public class FractionOrPercentageUtil {


    /**
     * 计算百分比，返回x%或者1/m
     *
     * @param m 人数
     * @return
     */
    public static String calculateResult(int m) {
        if (m <= 0 || m > 100) {
            return null;
        }

        int result = 100 / m;
        int remainder = 100 % m;

        if (remainder == 0) {
            return result + "%";
        } else {
            return "1/" + m;
        }
    }

    /**
     * 判断字符串是分数还是百分数
     *
     * @param input
     * @return 分数|百分数
     */
    public static String checkType(String input) {
        if (input == null || input.isEmpty()) {
            return null;
        }

        // 去掉首尾空格
        input = input.trim();

        // 判断是否为分数
        if (input.contains("/")) {
            String[] parts = input.split("/");
            if (parts.length == 2) {
                try {
                    int numerator = Integer.parseInt(parts[0].trim());
                    int denominator = Integer.parseInt(parts[1].trim());
                    if (denominator != 0) {
                        return "分数"; // 是分数
                    }
                } catch (NumberFormatException e) {
                    return null; // 格式错误
                }
            }
        }

        // 判断是否为百分数
        if (input.endsWith("%") && input.length() > 1) {
            try {
                double percentageValue = Double.parseDouble(input.substring(0, input.length() - 1));
                return "百分数"; // 是百分数
            } catch (NumberFormatException e) {
                return null;
            }
        }

        try {
            double percentageValue = Double.parseDouble(input);
            return "百分数"; // 是百分数
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 计算多个分数或百分数的和
     *
     * @param inputs
     * @return 分数|百分数 可以除尽输出百分数,否则输出分数
     */
    public static String calculateSum(String... inputs) {
        if (inputs == null || inputs.length == 0) {
            return "0%";
        }

        // 初始化分子和分母
        int numerator = 0; // 分子
        int denominator = 1; // 分母

        for (String input : inputs) {
            if (input.contains("/")) {
                // 处理分数形式 "1/3"
                String[] parts = input.split("/");
                if (parts.length == 2) {
                    try {
                        int currentNumerator = Integer.parseInt(parts[0].trim());
                        int currentDenominator = Integer.parseInt(parts[1].trim());

                        // 分数加法：(a/b + c/d) = (ad + bc) / bd
                        numerator = numerator * currentDenominator + currentNumerator * denominator;
                        denominator = denominator * currentDenominator;

                        // 简化分数
                        int gcdValue = gcd(numerator, denominator);
                        numerator /= gcdValue;
                        denominator /= gcdValue;
                    } catch (NumberFormatException e) {
                        throw new IllegalArgumentException("无效的分数格式: " + input);
                    }
                } else {
                    throw new IllegalArgumentException("无效的分数格式: " + input);
                }
            } else if (input.endsWith("%")) {
                // 处理百分数形式 "10%"
                try {
                    int percentage = Integer.parseInt(input.substring(0, input.length() - 1).trim());

                    // 将百分数转换为分数形式 (percentage/100)
                    int currentNumerator = percentage;
                    int currentDenominator = 100;

                    // 分数加法：(a/b + c/d) = (ad + bc) / bd
                    numerator = numerator * currentDenominator + currentNumerator * denominator;
                    denominator = denominator * currentDenominator;

                    // 简化分数
                    int gcdValue = gcd(numerator, denominator);
                    numerator /= gcdValue;
                    denominator /= gcdValue;
                } catch (NumberFormatException e) {
                    throw new IllegalArgumentException("无效的百分数格式: " + input);
                }
            } else {
                throw new IllegalArgumentException("未知格式: " + input);
            }
        }

        // 判断是否能整除
        double totalAsDecimal = (double) numerator / denominator;
        double percentageResult = totalAsDecimal * 100;

        if (Math.abs(percentageResult - Math.round(percentageResult)) < 1e-9) {
            // 能整除，返回百分数形式
            return Math.round(percentageResult) + "%";
        } else {
            // 不能整除，返回分数形式
            return numerator + "/" + denominator;
        }
    }

    /**
     * 计算两个分数或百分数的差值
     *
     * @param num1
     * @param num2
     * @return 分数|百分数 可以除尽输出百分数,否则输出分数
     */
    public static String calculateSubtract(String num1, String num2) {
        Fraction fraction1 = parseFraction(num1);
        Fraction fraction2 = parseFraction(num2);
        Fraction result = fraction1.subtract(fraction2);
        if (result.canBeConvertedToPercentage()) {
            return result.toPercentage();
        } else {
            return result.toString();
        }
    }

    // 分数类，用于表示分数并进行相关操作
    static class Fraction {
        BigInteger numerator;
        BigInteger denominator;

        // 构造函数，用于初始化分数
        public Fraction(BigInteger numerator, BigInteger denominator) {
            if (denominator.equals(BigInteger.ZERO)) {
                throw new IllegalArgumentException("分母不能为 0");
            }
            BigInteger gcd = numerator.gcd(denominator);
            this.numerator = numerator.divide(gcd);
            this.denominator = denominator.divide(gcd);
        }

        // 减法方法，将两个分数相减
        public Fraction subtract(Fraction other) {
            BigInteger commonDenominator = this.denominator.multiply(other.denominator);
            BigInteger newNumerator = this.numerator.multiply(other.denominator)
                    .subtract(other.numerator.multiply(this.denominator));
            return new Fraction(newNumerator, commonDenominator);
        }

        // 判断分数是否能转换为百分数
        public boolean canBeConvertedToPercentage() {
            double totalAsDecimal = numerator.doubleValue() / denominator.doubleValue();
            double percentageResult = totalAsDecimal * 100;
            return Math.abs(percentageResult - Math.round(percentageResult)) < 1e-9;
        }

        // 将分数转换为百分数
        public String toPercentage() {
            double totalAsDecimal = numerator.doubleValue() / denominator.doubleValue();
            double percentageResult = totalAsDecimal * 100;
            return Math.round(percentageResult) + "%";
        }

        @Override
        public String toString() {
            if (denominator.equals(BigInteger.ONE)) {
                return numerator.toString();
            }
            return numerator + "/" + denominator;
        }
    }

    // 解析输入的字符串为分数
    private static Fraction parseFraction(String input) {
        if (input.endsWith("%")) {
            String numberPart = input.substring(0, input.length() - 1);
            return new Fraction(new BigInteger(numberPart), BigInteger.valueOf(100));
        } else if (input.contains("/")) {
            String[] parts = input.split("/");

            return new Fraction(new BigInteger(parts[0]), new BigInteger(parts[1]));
        } else {
            return new Fraction(new BigInteger(input), BigInteger.ONE);
        }
    }

    // 辅助方法：计算最大公约数
    private static int gcd(int a, int b) {
        while (b != 0) {
            int temp = b;
            b = a % b;
            a = temp;
        }
        return a;
    }

    public static void main(String[] args) {
        // 判断字符串是分数还是百分数
        System.out.println(checkType("1/3"));
        System.out.println(checkType("30%"));
        System.out.println(checkType("abc"));
        System.out.println(checkType("50 / 100"));
        System.out.println(checkType("100%"));
        System.out.println(checkType("100/%"));
        System.out.println(checkType("25.55%"));
        System.out.println(checkType("25.55"));
        // 计算百分比
        System.out.println(calculateResult(5));   // 输出: 20%
        System.out.println(calculateResult(3));   // 输出: 1/3
        System.out.println(calculateResult(25));  // 输出: 4%
        System.out.println(calculateResult(7));   // 输出: 1/7
        System.out.println(calculateResult(0));   // 输出: Invalid input
        System.out.println(calculateResult(101)); // 输出: Invalid input
        //计算多个分数或百分数的和
        String[] values = {
                "10%",      // 10%
                "1/5",      // 20%
                "15%",     // 15%
                "1/10",     // 10%
                "1/20",     // 5%
                "1/4",      // 25%
                "9%",       // 9%
                "1/50",     // 2%
                "3/100",    // 3%
                "1/100"     // 1%
        };
        System.out.println(calculateSum("10%", "3/7", "3/70"));
        System.out.println(calculateSum(values));
        // 计算两个分数的差值
        System.out.println(calculateSubtract("3/4", "1/2"));
        System.out.println(calculateSubtract("75%", "50%"));
        System.out.println(calculateSubtract("2/3", "1/4"));
        System.out.println(calculateSubtract("25%", "1/6"));
    }
}
