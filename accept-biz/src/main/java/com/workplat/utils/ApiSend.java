package com.workplat.utils;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.workplat.gss.common.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * @Author: Odin
 * @Date: 2024/10/23 10:09
 * @Description: 通用请求工具类
 */

@Slf4j
public class ApiSend {

    // =============POST请求=============== //
    public static String post(String url, String bodyJson, String... headerKV) {
        log.info("请求接口:{} - 接口入参:{}", url, bodyJson.toString());
        HttpRequest request = new HttpRequest(url);
        if (headerKV.length % 2 != 0) {
            throw new BusinessException("请传入正确格式的请求头类型和值");
        }
        for (int i = 0; i < headerKV.length; i += 2) {
            request.header(headerKV[i], headerKV[i + 1]);
        }
        HttpResponse response = request.body(bodyJson).execute();
        log.info("接口响应码:{} - 接口响应值:{}", response.getStatus(), response.body());
        return response.body();
    }

    public static String post(String url, Map<String, Object> param, String... headerKV) {
        log.info("请求接口:{} - 接口入参:{}", url, param.toString());
        HttpRequest request = new HttpRequest(url);
        if (headerKV.length % 2 != 0) {
            throw new BusinessException("请传入正确格式的请求头类型和值");
        }
        for (int i = 0; i < headerKV.length; i += 2) {
            request.header(headerKV[i], headerKV[i + 1]);
        }
        HttpResponse response = request.form(param).execute();
        log.info("接口响应码:{} - 接口响应值:{}", response.getStatus(), response.body());
        return response.body();
    }

    public static String post(String url, String bodyJson) {
        log.info("请求接口:{} - 接口入参:{}", url, bodyJson);
        HttpResponse response = HttpRequest.post(url).body(bodyJson).execute();
        log.info("接口响应码:{} - 接口响应值:{}", response.getStatus(), response.body());
        return response.body();
    }

    public static String post(String url, Map<String, Object> param) {
        log.info("请求接口:{} - 接口入参:{}", url, param.toString());
        HttpResponse response = HttpRequest.post(url).form(param).execute();
        log.info("接口响应码:{} - 接口响应值:{}", response.getStatus(), response.body());
        return response.body();
    }

    // =============GET请求=============== //
    public static String get(String url, String param, String... headerKV) {
        log.info("请求接口:{} - 接口入参:{}", url, param);
        HttpRequest request = new HttpRequest(url + param);
        if (headerKV.length % 2 != 0) {
            throw new BusinessException("请传入正确格式的请求头类型和值");
        }
        for (int i = 0; i < headerKV.length; i += 2) {
            request.header(headerKV[i], headerKV[i + 1]);
        }
        HttpResponse response = request.execute();
        log.info("接口响应码:{} - 接口响应值:{}", response.getStatus(), response.body());
        return response.body();
    }

    public static String get(String url, String param) {
        log.info("请求接口:{} - 接口入参:{}", url, param);
        HttpResponse response = HttpRequest.get(url + param).execute();
        log.info("接口响应码:{} - 接口响应值:{}", response.getStatus(), response.body());
        return response.body();
    }

}
