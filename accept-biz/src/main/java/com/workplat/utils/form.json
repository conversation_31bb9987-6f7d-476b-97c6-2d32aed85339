{"tableName": "", "renderList": [{"id": "pepk8Ilwya", "icon": "icon-<PERSON><PERSON><PERSON>", "name": "表单域", "type": "formArea", "child": [{"id": "QNKFTpk-g", "key": "", "icon": "icon-da<PERSON><PERSON><PERSON><PERSON>", "name": "单选框", "child": [], "props": {"id": "86d7df3193bd48fc9653d54c3905d817", "key": "zffs", "field": "zffs", "label": "支付方式", "options": [{"label": "全款", "value": "zffsqk", "disabled": false}, {"label": "分期", "value": "zffsfq", "disabled": false}, {"label": "贷款", "value": "zffsdk", "disabled": false}], "disabled": false, "functions": [{"key": "changezffs", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "labelHeight": "", "placeholder": "请选择"}, "rules": [{"message": "支付方式不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-radio"}, {"id": "e_qga1IOv", "key": "", "icon": "icon-da<PERSON><PERSON><PERSON><PERSON>", "name": "单选框", "child": [], "props": {"id": "a23cadf7ea3a4dad88d40c00fed968da", "key": "dkqk", "field": "dkqk", "label": "贷款情况", "options": [{"label": "全款付清", "value": "dkqk1", "disabled": false}, {"label": "贷款已还清", "value": "dkqk2", "disabled": false}, {"label": "未还清", "value": "dkqk3", "disabled": false}], "disabled": false, "functions": [{"key": "changedkqk", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "labelHeight": "", "placeholder": "请选择"}, "rules": [{"message": "贷款情况不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-radio"}, {"id": "I1whUyumU", "key": "", "icon": "icon-da<PERSON><PERSON><PERSON><PERSON>", "name": "单选框", "child": [], "props": {"id": "f02c04c600e7486b8cf2bdbaab583713", "key": "dkfs", "field": "dkfs", "label": "贷款方式", "options": [{"label": "纯商业贷款", "value": "dkfs1", "disabled": false}, {"label": "纯公积金贷款", "value": "dkfs2", "disabled": false}, {"label": "组合贷款", "value": "dkfs3", "disabled": false}], "disabled": false, "functions": [{"key": "changedkfs", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "labelHeight": "", "placeholder": "请选择"}, "rules": [{"message": "贷款方式不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-radio"}, {"id": "6imT8uwDW", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"id": "0be35615d56f45a3a396e30348665964", "key": "dkyh", "field": "dkyh", "label": "贷款银行", "default": "", "options": [{"label": "建设银行", "value": "dkyh1", "disabled": false}, {"label": "中国银行", "value": "dkyh2", "disabled": false}, {"label": "邮政储蓄", "value": "dkyh3", "disabled": false}, {"label": "工商银行", "value": "dkyh4", "disabled": false}, {"label": "农业银行", "value": "dkyh5", "disabled": false}, {"label": "农商行", "value": "dkyh6", "disabled": false}], "disabled": false, "functions": [{"key": "changedkyh", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "labelHeight": "", "placeholder": "请选择"}, "rules": [{"message": "贷款银行不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}, {"id": "0ICsvhyM6", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"id": "21ad508107bd46e8845d54c5d8948256", "key": "zhmc", "field": "zhmc", "label": "支行名称", "default": "", "options": [{"label": "选项一", "value": "1", "disabled": false}, {"label": "选项二", "value": "2", "disabled": false}], "disabled": false, "functions": [{"key": "changezhmc", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "labelDescribe": ""}, "rules": [{"message": "支行名称不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}], "props": {"show": true, "field": "2pDir9HHI", "title": "支付方式", "bgColor": "#dd4b39", "barColor": "", "isHidden": false, "functions": [], "titleSize": 22, "arrowColor": "#000000", "titleColor": "#0E0D0D", "isShowButton": true}, "rules": [], "events": {}, "platform": "all", "needSpecial": false, "componentName": "ANetFormArea"}, {"id": "QwJQa2oyG4", "icon": "icon-<PERSON><PERSON><PERSON>", "name": "可新增表格", "type": "addTable", "child": [{"child": [{"id": "1PdtEr3D-", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "df0fe3437a0d4e6094d332a35f15be00", "key": "fpdm", "field": "fpdm", "label": "发票代码", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputfpdm", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsfpdm", "name": "foucs事件", "value": ""}, {"key": "changefpdm", "name": "change事件", "value": ""}, {"key": "clickfpdm", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "labelDescribe": "20位发票号码可拆分为12位发票代码+8位发票号码", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "GV8f9624A", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "4835b58dea0d4114a558216bf790be9c", "key": "fphm", "field": "fphm", "label": "发票号码", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputfphm", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsfphm", "name": "foucs事件", "value": ""}, {"key": "changefphm", "name": "change事件", "value": ""}, {"key": "clickfphm", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "labelDescribe": "20位发票号码可拆分为12位发票代码+8位发票号码", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [{"message": "发票号码不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "zCOKz69jwS", "icon": "icon-tupian", "name": "图片", "child": [], "props": {"key": "", "url": "https://zwfw.taicang.gov.cn/gateway-api/allinone-api/api/file/download?fileId=395c7aab9f34458a9d850f882c6236ea", "field": "Tyj9hpBqm", "label": "发票样例", "width": "50px", "fileId": "", "height": "50px", "functions": [], "labelWidth": "", "tableWidth": "", "labelDescribe": "20位发票号码可拆分为12位发票代码+8位发票号码"}, "rules": [], "events": {}, "platform": "all", "needRules": false, "needSpecial": false, "componentName": "ANetImage"}]}], "props": {"key": "", "max": 1, "min": 1, "field": "wN249e2UP", "title": "购房发票信息", "addText": "新增", "functions": [{"key": "deletewN249e2UP", "name": "delete事件", "value": ""}, {"key": "addwN249e2UP", "name": "add事件", "value": ""}], "isNeedMax": false, "isNeedMin": false, "deleteText": "删除", "innerWidth": 1, "outerWidth": 1, "defaultLine": 0, "isSelection": false, "isShowIndex": true, "showColumns": "", "isAddByDialog": false, "isShowAsTable": false, "isShowOutBorder": true, "innerBorderColor": "#000", "outerBorderColor": "#000", "isShowInnerBorder": true}, "rules": [], "events": {}, "platform": "all", "needSpecial": false, "componentName": "ANetCanAddTable"}], "formAttribute": {"inline": false, "disabled": false, "tableName": "demoFormName", "labelWidth": "100px", "labelPosition": "right"}, "beforeFunction": "{}", "submitFunction": "{}"}