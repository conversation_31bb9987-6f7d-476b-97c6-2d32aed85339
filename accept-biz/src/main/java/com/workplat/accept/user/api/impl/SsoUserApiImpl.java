package com.workplat.accept.user.api.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.IdUtil;
import cn.hutool.http.server.HttpServerRequest;
import com.workplat.accept.user.api.SsoUserApi;
import com.workplat.accept.user.entity.SsoUser;
import com.workplat.accept.user.service.SsoUserService;
import com.workplat.accept.user.vo.SsoUserVO;
import com.workplat.gss.common.core.response.ResponseData;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: Odin
 * @Date: 2024/9/23 11:32
 * @Description:
 */

@RestController
public class SsoUserApiImpl implements SsoUserApi {

    @Resource
    private SsoUserService userService;

    /**
     * 根据用户ID返回信息
     * @param userId
     * @return
     */
    @Override
    public ResponseData<SsoUserVO> rmcLoginByUserId(String userId) {
        // TODO:测试用户
        if (userId.equals("0000")) {
            SsoUser ssoUser = userService.loadByUserId(userId);
            SsoUserVO ssoUserVO = SsoUserVO.builder().userId(ssoUser.getUserId())
                    .username(ssoUser.getUsername())
                    .certificateType(ssoUser.getCertificateType())
                    .certificateNumber(ssoUser.getCertificateNumber())
                    .token(StpUtil.getTokenValue()).build();
            return ResponseData.success(ssoUserVO);
        }
        // 调用RMC解析用户信息
        SsoUser rmcUser = userService.getUserInfoByUserId(userId);
        String aNetToken = userService.getANetToken(rmcUser.getRealName(), rmcUser.getCertificateNumber());
        // 使用Sa-Token登陆并返回Token
        StpUtil.login(rmcUser.getUserId());
        SsoUserVO ssoUserVO = SsoUserVO.builder().userId(rmcUser.getUserId())
                .username(rmcUser.getUsername())
                .certificateType(rmcUser.getCertificateType())
                .certificateNumber(rmcUser.getCertificateNumber())
                .aNetToken(aNetToken)
                .token(StpUtil.getTokenValue()).build();
        return ResponseData.success(ssoUserVO);
    }

    /**
     * 退出登陆
     *
     * @return
     */
    @Override
    public ResponseData logout() {
        StpUtil.logout();
        return ResponseData.success().build();
    }

    @Override
    public ResponseData getANetToken(String name, String idCard) {
        return ResponseData.success(userService.getANetToken(name, idCard));
    }

}
