package com.workplat.accept.business.chat.convert;

import com.google.common.collect.ImmutableMap;
import com.workplat.accept.business.chat.entity.BizChatConversation;
import com.workplat.accept.business.chat.entity.BizChatMessage;
import com.workplat.accept.business.chat.service.BizChatConversationService;
import com.workplat.accept.business.chat.vo.BizChatMessageVO;
import com.workplat.gss.common.core.converter.BaseConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
public class BizChatMessageVOConvert implements BaseConverter<BizChatMessage, BizChatMessageVO> {

    @Autowired
    private BizChatConversationService bizChatConversationService;

    @Override
    public BizChatMessageVO convert(BizChatMessage source) {
        BizChatConversation bizChatConversation = bizChatConversationService
                .queryForSingle(ImmutableMap.of("=(id)", source.getConversationId()));

        BizChatMessageVO target = BaseConverter.super.convert(source);
        if (bizChatConversation != null && target != null) {
            target.setAgentKey(bizChatConversation.getAgentKey());
            target.setAgentChatId(bizChatConversation.getAgentChatId());
            target.setInstanceId(bizChatConversation.getInstanceId());
        }
        return target;
    }
}
