package com.workplat.accept.business.chat.service.Impl;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.workplat.accept.business.chat.dto.AskDTO;
import com.workplat.accept.business.chat.dto.ChatProcessDTO;
import com.workplat.accept.business.chat.service.DifyServiceHelper;
import com.workplat.accept.business.chat.vo.StreamMessageVO;
import com.workplat.utils.ChatCacheUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class ChatStreamService {
    
    private final DifyServiceHelper difyServiceHelper;
    private final ChatMessageService chatMessageService;
    private final ChatCacheUtil chatCacheUtil;

    public ChatStreamService(DifyServiceHelper difyServiceHelper,
                             ChatMessageService chatMessageService,
                              ChatCacheUtil chatCacheUtil) {
        this.difyServiceHelper = difyServiceHelper;
        this.chatMessageService = chatMessageService;
        this.chatCacheUtil = chatCacheUtil;
    }
    
    public Flux<ServerSentEvent<String>> processStreamMessages(AskDTO ask, Flux<StreamMessageVO> streamMessageVOFlux) {
        StringBuilder aiResponseBuilder = new StringBuilder();
        StringBuilder thoughtsBuilder = new StringBuilder();
        final boolean[] isThinking = {false}; // 使用数组来让lambda可以修改
        
        return streamMessageVOFlux
                .flatMap(streamMessageVO -> {
                    streamMessageVO.setRecordId(ask.getRecordId());
                    try {
                        if (streamMessageVO.getAnswer() != null) {
                            String answer = streamMessageVO.getAnswer();
                            
                            // 检查是否包含思考开始标记
                            if (answer.contains("<think>")) {
                                isThinking[0] = true;
                                answer = answer.replace("<think>", "");
                            }
                            
                            // 检查是否包含思考结束标记
                            if (answer.contains("</think>")) {
                                isThinking[0] = false;
                                answer = answer.replace("\n</think>", "");
                            }
                            
                            // 根据思考状态处理内容
                            if (isThinking[0]) {
                                thoughtsBuilder.append(answer);
                                streamMessageVO.setThoughts(answer);
                                streamMessageVO.setAnswer(""); // 清空answer，因为这是思考内容
                            } else {
                                aiResponseBuilder.append(answer);
                                streamMessageVO.setAnswer(answer);
                            }
                        }
                        
                        if ("message_end".equals(streamMessageVO.getEvent())) {
                            StreamMessageVO messageVO = new StreamMessageVO();
                            messageVO.setAnswer(aiResponseBuilder.toString());
                            messageVO.setThoughts(thoughtsBuilder.toString());
                            chatMessageService.saveAiMessage(ask.getRecordId(), JSONObject.toJSONString(messageVO));
                        }
                        return Flux.just(convertToServerSentEvent(streamMessageVO));
                    } catch (Exception e) {
                        log.error("Error processing stream message: {}", e.getMessage(), e);
                        return Flux.just(ServerSentEvent.<String>builder()
                                .event("error")
                                .data("Stream error: " + e.getMessage())
                                .build());
                    }
                });
    }
    
    private ServerSentEvent<String> convertToServerSentEvent(StreamMessageVO streamMessageVO) throws JsonProcessingException {
        log.info("convertToServerSentEvent: {}", new ObjectMapper().writeValueAsString(streamMessageVO));
        return ServerSentEvent.<String>builder()
                .id(streamMessageVO.getId())
                .event(streamMessageVO.getEvent())
                .data(new ObjectMapper().writeValueAsString(streamMessageVO))
                .build();
    }
}