package com.workplat.accept.business.chat.service.Impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSONObject;
import com.workplat.accept.business.chat.convert.BizChatMessageVOConvert;
import com.workplat.accept.business.chat.entity.BizChatMessage;
import com.workplat.accept.business.chat.service.BizChatMessageService;
import com.workplat.accept.business.chat.vo.BizChatMessageVO;
import com.workplat.componentEngine.dto.ComponentRunDTO;
import com.workplat.gss.common.core.service.impl.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class BizChatMessageServiceImpl extends BaseServiceImpl<BizChatMessage> implements BizChatMessageService {

    final BizChatMessageVOConvert bizChatMessageVOConvert;

    public BizChatMessageServiceImpl(BizChatMessageVOConvert bizChatMessageVOConvert) {
        this.bizChatMessageVOConvert = bizChatMessageVOConvert;
    }

    @Override
    public List<BizChatMessageVO> getMessages(String conversationId) {
        Sort sort = Sort.by(Sort.Direction.ASC, "createTime");
        List<BizChatMessage> bizChatMessages =
                queryForList(MapUtil.<String, Object>builder().put("=(conversationId)", conversationId).build(), sort);
        return bizChatMessageVOConvert.convert(bizChatMessages);
    }

    @Override
    public BizChatMessage createMessage(BizChatMessage message) {
        return super.save(message);
    }

    @Override
    public void UpdateComponentRun(ComponentRunDTO componentRunDto) {
        List<BizChatMessage> bizChatMessages =
                queryForList(MapUtil.<String, Object>builder().put("=(conversationId)", componentRunDto.getRecordId()).build());
        bizChatMessages.stream().filter(chatMessageVO -> chatMessageVO.getContent().contains(componentRunDto.getEngineCode()))
                .min((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime()))
                .ifPresent(chatMessageVO -> {
                    String content = chatMessageVO.getContent();
                    JSONObject jsonObject = JSONObject.parseObject(content);
                    jsonObject.put("renderData", componentRunDto.getRenderData());
                    chatMessageVO.setContent(jsonObject.toJSONString());
                    super.update(chatMessageVO);
                });
    }
}