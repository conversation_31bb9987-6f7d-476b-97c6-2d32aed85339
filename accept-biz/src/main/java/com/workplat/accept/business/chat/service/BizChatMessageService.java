package com.workplat.accept.business.chat.service;

import com.workplat.accept.business.chat.entity.BizChatMessage;
import com.workplat.accept.business.chat.vo.BizChatMessageVO;
import com.workplat.componentEngine.dto.ComponentRunDTO;
import com.workplat.gss.common.core.service.BaseService;

import java.util.List;

/**
 * 消息服务接口
 */
public interface BizChatMessageService extends BaseService<BizChatMessage> {
    /**
     * 根据会话ID查询消息列表
     *
     * @param conversationId 会话ID
     * @return 消息列表
     */
    List<BizChatMessageVO> getMessages(String conversationId);

    /**
     * 发送新消息
     *
     * @param message 消息对象
     * @return 创建后的消息对象
     */
    BizChatMessage createMessage(BizChatMessage message);

    void UpdateComponentRun(ComponentRunDTO componentRunDto);
} 