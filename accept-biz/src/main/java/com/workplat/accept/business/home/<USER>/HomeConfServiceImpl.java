package com.workplat.accept.business.home.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.workplat.accept.business.home.converter.PlatMatterCatalogAcceptConvert;
import com.workplat.accept.business.home.dto.ConfMatterCatalogDto;
import com.workplat.accept.business.home.dto.HomeConfDto;
import com.workplat.accept.business.home.entity.HomeConf;
import com.workplat.accept.business.home.vo.ConfMatterCatalogVO;
import com.workplat.accept.business.home.vo.HomeConfVO;
import com.workplat.gss.common.core.exception.BusinessException;
import com.workplat.gss.common.core.service.impl.BaseServiceImpl;
import com.workplat.gss.common.core.util.CacheDictUtils;
import com.workplat.gss.service.item.dubbo.matter.entity.ConfMatter;
import com.workplat.gss.service.item.dubbo.matter.entity.ConfMatterCatalog;
import com.workplat.gss.service.item.dubbo.matter.entity.accept.ConfMatterAccept;
import com.workplat.gss.service.item.dubbo.matter.entity.distribute.ConfMatterDistribute;
import com.workplat.gss.service.item.dubbo.matter.service.ConfMatterService;
import com.workplat.gss.service.item.dubbo.matter.service.accept.ConfMatterAcceptService;
import com.workplat.gss.service.item.dubbo.matter.service.distribute.ConfMatterDistributeService;
import com.workplat.gss.service.item.dubbo.online.constant.OnlineStatusEnum;
import com.workplat.gss.service.item.dubbo.online.entity.ConfMatterOnline;
import com.workplat.gss.service.item.dubbo.online.service.ConfMatterOnlineService;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: Odin
 * @Date: 2024/9/24 16:47
 * @Description:
 */
@Service
public class HomeConfServiceImpl extends BaseServiceImpl<HomeConf> implements HomeConfService {

    @Resource
    private ConfMatterService confMatterService;
    @Resource
    private ConfMatterOnlineService confMatterOnlineService;
    @Resource
    private ConfMatterDistributeService confMatterDistributeService;
    @Resource
    private ConfMatterAcceptService confMatterAcceptService;
    @Resource
    private PlatMatterCatalogAcceptConvert platMatterCatalogAcceptConvert;
    @Resource
    private ConfMatterCatalogAcceptService confMatterCatalogAcceptService;

    @Override
    public LinkedHashMap<String, List<HomeConfVO>> getHomeConfList() {
        LinkedHashMap<String, List<HomeConfVO>> homeConfMap = new LinkedHashMap<>();
        Map<String, String> matterTypeDict = CacheDictUtils.getDictMapByCode("BDC_MATTER_TYPE");

        LinkedHashSet<String> matterTypes = new LinkedHashSet<>();
        matterTypeDict.forEach((matterTypeCode, matterTypeValue) -> matterTypes.add(matterTypeValue));
        // 查询已经上线的事项
        List<ConfMatterOnline> confMatterOnlineList = confMatterOnlineService.queryForList(
                MapUtil.<String, Object>builder().put("=(onlineStatusEnum)", OnlineStatusEnum.ONLINE).build());
        List<ConfMatter> confMatterList = new ArrayList<>();
        confMatterOnlineList.forEach(confMatterOnline -> {
            confMatterList.add(confMatterOnline.getConfMatter());
        });

        // 遍历父级菜单，存入Map
        for (String matterType : matterTypes) {
            List<ConfMatter> confMatters = confMatterList.stream()
                    .filter(confMatter -> matterType.equals(confMatter.getMatterType()))
                    .collect(Collectors.toList());
            List<HomeConfVO> homeConfVOList = new ArrayList<>();
            confMatters.forEach(confMatter -> {
                homeConfVOList.add(this.queryByConfMatterId(confMatter.getId()));
            });
            homeConfMap.put(matterType, homeConfVOList);
        }
        return homeConfMap;
    }

    @Override
    public void saveHomeConf(HomeConfDto homeConfDto) {
        // * 判断外键存在情况
        ConfMatter confMatter = confMatterService.queryById(homeConfDto.getRelatedConfMatterId());
        if (ObjUtil.isNull(confMatter)) {
            throw new BusinessException("找不到关联的事项(ConfMatter.id)");
        }
        confMatter.setMatterName(homeConfDto.getMatterName());
        confMatter.setMatterType(homeConfDto.getMatterType());
        confMatterService.save(confMatter);

        ConfMatterCatalog confMatterCatalog = confMatter.getCatalog();
        confMatterCatalog.setMatterName(homeConfDto.getMatterName());
        confMatterCatalog.setMatterType(homeConfDto.getMatterType());
        confMatterCatalogAcceptService.update(confMatterCatalog);

        ConfMatterOnline confMatterOnline = confMatterOnlineService.queryForSingle
                (Map.of("=(confMatter)", confMatter));
        confMatterOnline.setMatterName(confMatter.getMatterName());
        confMatterOnlineService.save(confMatterOnline);

        HomeConf homeConf = this.queryForSingle(MapUtil.<String, Object>builder().put("=(relatedConfMatterId)", homeConfDto.getRelatedConfMatterId()).build());
        homeConf.setBusinessDesc(homeConfDto.getBusinessDesc());
        homeConf.setIconAddress(homeConfDto.getIconAddress());
        homeConf.setHoverIconAddress(homeConfDto.getHoverIconAddress());
        homeConf.setSkipUrl(homeConfDto.getSkipUrl());
        homeConf.setIsExternalLink(homeConfDto.getIsExternalLink());
        homeConf.setMenuStatus(homeConfDto.getMenusStatus());
        homeConf.setMenuRemark(homeConfDto.getMenuRemark());
        this.update(homeConf);
    }

    @Override
    public HomeConfVO queryByConfMatterId(String confMatterId) {
        HomeConf homeConf = this.queryForSingle(MapUtil.<String, Object>builder().put("=(relatedConfMatterId)", confMatterId).build());
        ConfMatter confMatter = confMatterService.queryById(confMatterId);
        HomeConfVO homeConfVO = BeanUtil.copyProperties(homeConf, HomeConfVO.class);
        homeConfVO.setMatterName(confMatter.getMatterName());
        homeConfVO.setMatterType(confMatter.getMatterType());
        return homeConfVO;
    }

    @Override
    public void deleteMatter(String id) {
        confMatterCatalogAcceptService.deleteById(id);
        Map<String, Object> matterQueryMap = MapUtil.<String, Object>builder().put("=(catalogId)", id).build();
        ConfMatter confMatter = confMatterService.queryForSingle(matterQueryMap);
        String matterId = confMatter.getId();
        confMatterService.deleteById(matterId);
        this.deleteByParams(Map.of("=(relatedConfMatterId)", matterId));
        List<ConfMatterOnline> confMatterOnlineList = confMatterOnlineService.queryForList(
                MapUtil.<String, Object>builder().put("=(onlineStatusEnum)", OnlineStatusEnum.ONLINE)
                        .put("=(confMatter)", confMatter).build());
        if (CollectionUtil.isNotEmpty(confMatterOnlineList)) {
            throw new BusinessException("事项已上线，无法删除");
        }
        confMatterOnlineService.deleteByParams(MapUtil.<String, Object>builder().put("=(confMatter)", confMatter).build());
    }

    @Override
    public ConfMatterCatalogVO saveMatter(ConfMatterCatalogDto confMatterCatalogDto) {
        ConfMatterCatalog confMatterCatalog = BeanUtil.copyProperties(confMatterCatalogDto, ConfMatterCatalog.class);
        String modelCode = confMatterCatalogDto.getMatterCode();
        List<ConfMatterCatalog> seals = confMatterCatalogAcceptService.queryForList(Map.of("=(matterCode)", modelCode));
        if (StrUtil.isNotEmpty(confMatterCatalogDto.getId())) {
            if (seals.size() > 1) {
                throw new BusinessException("事项编码不能重复");
            }
        } else {
            if (CollectionUtil.isNotEmpty(seals)) {
                throw new BusinessException("事项编码不能重复");
            }
        }
        ConfMatterCatalog save = confMatterCatalogAcceptService.saveNonNull(confMatterCatalog);
        ConfMatter matter = new ConfMatter();
        matter.setCatalogId(save.getId());
        matter.setMatterName(save.getMatterName());
        matter.setMatterCode(save.getMatterCode());
        matter.setRegion(save.getMatterLevel());
        matter.setMatterType(save.getMatterType());
        matter.setIsNormal(false);
        ConfMatter confMatter = confMatterService.saveNonNull(matter);
        ConfMatterAccept confMatterAccept = new ConfMatterAccept();
        confMatterAccept.setMatterId(confMatter.getId());
        confMatterAcceptService.save(confMatterAccept);
        ConfMatterDistribute confMatterDistribute = new ConfMatterDistribute();
        confMatterDistribute.setMatterId(confMatter.getId());
        confMatterDistributeService.save(confMatterDistribute);
        this.save(HomeConf.builder().relatedConfMatterId(confMatter.getId()).menuStatus("0").build());
        return platMatterCatalogAcceptConvert.convert(save);
    }

}
