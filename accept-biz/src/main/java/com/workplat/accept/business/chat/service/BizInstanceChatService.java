package com.workplat.accept.business.chat.service;

import com.workplat.accept.business.chat.dto.BizInstanceChatDTO;
import com.workplat.accept.business.chat.entity.BizInstanceChat;
import com.workplat.accept.business.chat.vo.BizInstanceChatVO;
import com.workplat.gss.common.core.service.BaseService;

import java.util.List;

public interface BizInstanceChatService extends BaseService<BizInstanceChat> {

    List<BizInstanceChatVO> getList(BizInstanceChatDTO dto);
}
