package com.workplat.accept.business.third.convert;

import com.workplat.accept.business.third.entity.ThirdData;
import com.workplat.accept.business.third.vo.ThirdDataVO;
import com.workplat.accept.user.entity.SsoUser;
import com.workplat.accept.user.service.SsoUserService;
import com.workplat.gss.common.core.converter.BaseConverter;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * @Author: Odin
 * @Date: 2024/9/26 10:58
 * @Description:
 */

@Component
public class ThirdDataConverter implements BaseConverter<ThirdData, ThirdDataVO> {

}
