package com.workplat.accept.business.mapping;

import jakarta.persistence.Column;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Comment;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfFieldMapping   {
    
    @Comment("第三方到模板的映射")
//    @Column(name = "third_to_template", columnDefinition = "TEXT")
    private String thirdToTemplate;  // JSON格式，包含路径和类型信息
    
    @Comment("模板到表单的映射")
//    @Column(name = "template_to_form", columnDefinition = "TEXT")
    private String templateToForm;   // JSON格式
}