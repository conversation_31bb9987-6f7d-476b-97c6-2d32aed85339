package com.workplat.accept.business.third.api.impl;

import com.workplat.accept.business.third.api.ThirdDataApi;
import com.workplat.accept.business.third.dto.ThirdDataQueryDto;
import com.workplat.accept.business.third.service.ThirdDataService;
import com.workplat.accept.business.third.vo.ThirdDataVO;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.log.annotation.ApiLogging;
import com.workplat.gss.log.constant.OperationType;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: Odin
 * @Date: 2024/9/26 10:07
 * @Description:
 */

@RestController
public class ThirdDataApiImpl implements ThirdDataApi {

    @Resource
    private ThirdDataService thirdDataService;

    @Override
    @ApiLogging(module = "个人中心查询", operation = "业务查询", type = OperationType.QUERY)
    public ResponseData<ThirdDataVO> queryThirdDataList(@RequestBody ThirdDataQueryDto dto) {
        ThirdDataVO thirdDataVOPage = thirdDataService.queryThirdDataList(dto);
        return ResponseData.success(thirdDataVOPage);
    }

}
