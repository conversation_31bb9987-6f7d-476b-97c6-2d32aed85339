package com.workplat.accept.business.mapping;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONPath;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class DataMappingService {

    @Autowired
    private FieldMappingConfig fieldMappingConfig;

    /**
     * 将第三方数据转换为模板展示数据
     */
    public Object convertToTemplateData(String thirdPartyJson) {
        try {
            JSONObject jsonObject = JSON.parseObject(thirdPartyJson);
            Map<String, String> mappings = JSON.parseObject(
                    fieldMappingConfig.getThirdToTemplate(),
                    new TypeReference<>() {
                    }
            );

            // 获取第一个映射路径来判断数据结构
            String firstPath = mappings.keySet().iterator().next();
            // 获取到数组的父路径，例如从 "data[*].htbh" 得到 "data"
            String arrayPath = firstPath.contains("[*]") ?
                    firstPath.substring(0, firstPath.indexOf("[*]")) : null;

            if (arrayPath != null) {
                // 处理数组数据
                JSONArray dataArray = (JSONArray) JSONPath.eval(jsonObject, arrayPath);
                if (dataArray == null) {
                    return Collections.emptyList();
                }

                List<Map<String, Object>> result = new ArrayList<>();
                for (int i = 0; i < dataArray.size(); i++) {
                    Map<String, Object> mappedItem = new HashMap<>();

                    int finalI = i;
                    mappings.forEach((sourcePath, targetField) -> {
                        // 替换通配符 [*] 为具体索引 [i]
                        String actualPath = sourcePath.replace("[*]", "[" + finalI + "]");
                        Object value = JSONPath.eval(jsonObject, actualPath);
                        if (value != null) {
                            mappedItem.put(targetField, value);
                        }
                    });

                    if (!mappedItem.isEmpty()) {
                        result.add(mappedItem);
                    }
                }
                return result;
            } else {
                // 处理对象数据
                Map<String, Object> result = new HashMap<>();
                mappings.forEach((sourcePath, targetField) -> {
                    Object value = JSONPath.eval(jsonObject, sourcePath);
                    if (value != null) {
                        result.put(targetField, value);
                    }
                });
                return result;
            }

        } catch (Exception e) {
            log.error("转换第三方数据失败", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 将模板数据转换为表单数据
     */
    public Object convertToFormData(Object templateData) {
        Map<String, String> formMappings = JSON.parseObject(
                fieldMappingConfig.getTemplateToForm(),
                new TypeReference<>() {
                }
        );

        if (templateData instanceof Map) {
            return mapToFormData((Map<String, Object>) templateData, formMappings);
        } else if (templateData instanceof List) {
            List<Map<String, Object>> result = new ArrayList<>();
            for (Object item : (List<?>) templateData) {
                if (item instanceof Map) {
                    result.add(mapToFormData((Map<String, Object>) item, formMappings));
                }
            }
            return result;
        }

        return Collections.emptyMap();
    }

    private Map<String, Object> mapToFormData(Map<String, Object> templateData,
                                              Map<String, String> formMappings) {
        Map<String, Object> formData = new HashMap<>();
        formMappings.forEach((templateField, formField) -> {
            if (templateData.containsKey(templateField)) {
                formData.put(formField, templateData.get(templateField));
            }
        });
        return formData;
    }
}