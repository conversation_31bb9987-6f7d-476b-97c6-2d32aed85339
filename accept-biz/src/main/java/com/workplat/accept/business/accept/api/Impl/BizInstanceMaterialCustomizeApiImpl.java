package com.workplat.accept.business.accept.api.Impl;

import com.workplat.accept.business.accept.api.BizInstanceMaterialCustomizeApi;
import com.workplat.gss.application.api.dto.BizInstanceInfoGetMaterialDTO;
import com.workplat.gss.application.biz.converter.BizInstanceMaterialConvert;
import com.workplat.gss.application.dubbo.dto.BizInstanceMaterialSubmitDTO;
import com.workplat.gss.application.dubbo.entity.BizInstanceMaterial;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.service.BizInstanceMaterialFileService;
import com.workplat.gss.application.dubbo.service.BizInstanceMaterialGroupService;
import com.workplat.gss.application.dubbo.service.BizInstanceMaterialService;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialFileVO;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialVO;
import com.workplat.gss.common.core.annotation.Idempotent;
import com.workplat.gss.common.core.autowired.GssAutowired;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.script.dubbo.loader.ScriptRunnerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;


@Slf4j
@Controller
@Transactional(rollbackFor = Exception.class)
public class BizInstanceMaterialCustomizeApiImpl implements BizInstanceMaterialCustomizeApi {


    @Autowired
    private BizInstanceMaterialService bizInstanceMaterialService;

    @Override
    public ResponseData<List<BizInstanceMaterialFileVO>> getSignFileList(BizInstanceInfoGetMaterialDTO instanceInfoGetMaterialDTO) {
        List<BizInstanceMaterialVO> instanceMaterialVOS =
                bizInstanceMaterialService.getInstanceMaterial(instanceInfoGetMaterialDTO.getInstanceId());
        // 获取签名文件列表
        List<BizInstanceMaterialFileVO> materialFileVOList = new ArrayList<>();

        instanceMaterialVOS.forEach(materialVO -> {
            if (materialVO.isSign() && materialVO.getSignType().equals("SIGNATURE")) {
                Collection<BizInstanceMaterial.MaterialFile> backTable = materialVO.getBackTable();
                BizInstanceMaterialFileVO vo = new BizInstanceMaterialFileVO();
                vo.setFileId(backTable.iterator().next().getBackFileId());
                vo.setFileName(backTable.iterator().next().getBackFileName());
                vo.setFileType("application/pdf");
                materialFileVOList.add(vo);
            }
        });
        return ResponseData.success().data(materialFileVOList);
    }
}
