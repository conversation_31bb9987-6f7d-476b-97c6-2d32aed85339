package com.workplat.flow.service.impl;

import com.workplat.flow.entity.ConfFlowComponent;
import com.workplat.flow.repository.ConfFlowComponentRepository;
import com.workplat.componentEngine.request.ComponentUsageRequest;
import com.workplat.flow.service.ConfFlowComponentService;
import com.workplat.componentEngine.vo.ComponentUsageSituationVO;
import com.workplat.gss.common.core.service.impl.BaseServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @author: qian cheng
 * @package: com.workplat.flow.service.impl
 * @description: 流程组件ServiceImpl
 * @date: 2025/5/20 10:01
 */
@Service
public class ConfFlowComponentServiceImpl extends BaseServiceImpl<ConfFlowComponent> implements ConfFlowComponentService {

    @Autowired
    ConfFlowComponentRepository confFlowComponentRepository;

    @Override
    public Page<ComponentUsageSituationVO> queryComponentUsage(ComponentUsageRequest request) {
        Pageable pageable = PageRequest.of(request.getPageNumber() - 1, request.getPageSize());
        if (StringUtils.isBlank(request.getName())){
            request.setName(null);
        }
        Page<Map<String, Object>> page = confFlowComponentRepository.queryComponentUsage(request.getName(), pageable);
        List<ComponentUsageSituationVO> voList = page
                .getContent()
                .stream()
                .map(map -> new ComponentUsageSituationVO(
                        (String) map.get("id"),
                        (String) map.get("code"),
                        (String) map.get("name"),
                        (String) map.get("description"),
                        (Date) map.get("createTime"),
                        (Date) map.get("updateTime"),
                        ((Number) map.get("itemCount")).intValue()
                )).toList();
        return new PageImpl<>(voList, pageable, page.getTotalElements());
    }
}
