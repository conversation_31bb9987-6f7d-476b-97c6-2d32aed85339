package com.workplat.flow.entity;

import com.workplat.gss.common.core.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

/**
 * @author: q<PERSON> cheng
 * @package: com.workplat.flow.entity
 * @description: 流程信息
 * @date: 2025/5/20 9:24
 */
@Setter
@Getter
@Entity
@Table(name = "conf_flow")
public class ConfFlow extends BaseEntity {

    @Comment("流程名称")
    @Column(name = "name", length = 50)
    private String name;

    @Comment("流程编码")
    @Column(name = "code", length = 32)
    private String code;

    @Comment("流程描述")
    @Column(name = "description")
    private String description;

}
