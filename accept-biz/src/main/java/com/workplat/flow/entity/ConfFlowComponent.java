package com.workplat.flow.entity;

import com.workplat.conf.component.entity.ConfComponent;
import com.workplat.gss.common.core.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

/**
 * @author: q<PERSON> cheng
 * @package: com.workplat.flow.entity
 * @description: 流程组件关联表
 * @date: 2025/5/20 9:32
 */
@Setter
@Getter
@Entity
@Table(name = "conf_flow_component")
public class ConfFlowComponent extends BaseEntity {

    @ManyToOne
    @Comment("流程信息")
    @JoinColumn(name = "conf_flow_id")
    private ConfFlow confFlow;

    @ManyToOne
    @Comment("组件类型")
    @JoinColumn(name = "conf_component_id")
    private ConfComponent confComponent;

    @Comment("组件顺序")
    @Column(name = "sequence_order", nullable = false, columnDefinition = "int default 0")
    private int sequenceOrder;

    /**
     * 备用字段
     */
    @Comment("扩展信息/特定配置")
    @Column(name = "configuration")
    private String configuration;

}
