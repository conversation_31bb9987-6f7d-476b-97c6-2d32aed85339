#!/bin/sh
#这句尤为重要，唯一ID防止Jenkins任务结束时候自动关掉所有的子进程
export BUILD_ID=dontKillMe
export JAVA_HOME=/opt/java/jdk-21.0.4
export PATH=$PATH:$JAVA_HOME/bin

#项目路径
project_home=/home/<USER>
#需要部署的项目的名称
project_name=accept-biz
#正式环境地址 用来测试项目是否部署成功
test_address=http://***************:9500/actuator/health
#jenkins项目打包路径
jenkins_path=/jenkins

# 启动jar
function startupJar(){
	echo "*************** 启动jar ***************"
	cd $project_home
        nohup /opt/java/jdk-21.0.4/bin/java -jar -Xms1g -Xmx1g -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:9501 -Dserver.port=9500 -Ddubbo.protocol.port=35804 --add-opens java.base/java.net=ALL-UNNAMED --add-opens java.base/sun.net.www.protocol.https=ALL-UNNAMED $project_name.jar --spring.cloud.nacos.config.namespace=bdc-cloud-dev > $project_name.log 2>&1 &
	echo "*************** 等待启动完成 ***************"
	sleep 30s

	#查看url的返回码
	check_url_code () {
		if [[ $1 ]]; then
			echo "*************** 访问的url：$1 ***************"
			return $(curl -I -m 10 -o /dev/null -s -w %{http_code}  $1)
		else
			echo "*************** url不能为空 ***************"
			exit 1
		fi
	}

	max_try=15
	code=000
	trys=0

	# 判断jar是否启动成功
	while [[ code -eq 000  ]] && [[ trys -lt max_try ]]
	do
		check_url_code $test_address
		code=$?
		trys=`expr $trys + 1`
		echo "*************** 第 $trys 次访问 $test_address ,返回码为：$code ***************"
		sleep 10s
	done

	if [[ code -eq 200 ]]; then
		echo "*************** jar启动成功，$test_address 访问成功，访问次数：$trys ***************"
		exit 0
	else
		echo "*************** jar启动成功，$test_address 访问失败，访问次数：$trys ***************"
		exit 500
	fi
}

# 关闭jar
function shutdownJar(){
	pidlist=`ps -ef|grep -v grep |grep $project_name|awk '{print $2}'`
	function stop(){
		if [ "$pidlist" == "" ]
		  then
		  echo "*************** $project_name 已经关闭 ***************"
		else
			echo "*************** $project_name 进程号: $pidlist ***************"
			kill -9 $pidlist
			echo "*************** KILL $pidlist ***************"
		fi
	}

	stop
	pidlist2=`ps -ef|grep -v grep |grep $project_name|awk '{print $2}'`
	if [ "$pidlist2" == "" ]
		then
		echo "*************** 关闭 $project_name 成功 ***************"
	else
		echo "*************** 关闭 $project_name 失败 ***************"
	fi
}

# 打包项目
shutdownJar

cd $project_home
#echo "*************** 备份当前的ROOT.war ***************"
#mv $project_home/$project_name.jar $project_home/$project_name.jar.bak`date +%Y%m%d`

echo "*************** 复制jenkins生成的jar包到对应项目路径 ***************"
cp -r $jenkins_path/$project_name.jar $project_home/$project_name.jar

sleep 3s
startupJar
