# 业务判断组件使用说明

## 概述

业务判断组件是一个在ask方法中调用的组件，用于在处理用户请求之前进行各种业务逻辑判断。该组件可以检查实例状态、材料完整性、表单步骤有效性和用户权限等，并根据判断结果决定是否继续处理请求或返回相应的错误信息。

## 架构设计

### 核心组件

1. **BusinessJudgmentComponentEngine**: 业务判断组件引擎
   - 继承自AbstractComponentEngine
   - 提供performBusinessJudgment()方法执行具体的判断逻辑
   - 返回BusinessJudgmentResult对象包含判断结果

2. **BusinessJudgmentResultHandler**: 判断结果处理器
   - 处理判断结果，生成用户友好的响应消息
   - 决定是否需要中断流程
   - 提供判断结果的严重程度评估

3. **ChatApiImpl**: 修改后的ask方法
   - 在处理用户请求前调用业务判断
   - 根据判断结果决定后续流程

## 判断逻辑

### 检查项目

1. **实例状态检查** (instanceValid)
   - 检查ChatProcessDTO是否存在
   - 检查实例ID是否有效
   - 验证实例状态是否正常

2. **材料完整性检查** (materialsComplete)
   - 检查是否有必需的材料
   - 验证材料信息是否完整

3. **表单步骤检查** (formStepValid)
   - 检查表单步骤索引是否在有效范围内
   - 验证表单步骤信息是否正确

4. **用户权限检查** (userAuthorized)
   - 检查用户是否有执行当前操作的权限
   - 验证用户身份和权限设置

### 判断结果

判断结果包含以下信息：

```java
public class BusinessJudgmentResult {
    private boolean instanceValid;        // 实例是否有效
    private boolean materialsComplete;    // 材料是否完整
    private boolean formStepValid;        // 表单步骤是否有效
    private boolean userAuthorized;       // 用户是否有权限
    private boolean overallValid;         // 综合判断结果
    private String judgmentMessage;       // 判断消息
    private String nextAction;           // 建议的下一步操作
    private Map<String, Object> additionalData; // 附加数据
}
```

### 下一步操作类型

- **CONTINUE**: 所有检查通过，继续正常流程
- **INIT_INSTANCE**: 需要初始化业务实例
- **UPLOAD_MATERIALS**: 需要上传材料
- **FIX_FORM_STEP**: 需要修复表单步骤
- **CHECK_AUTHORIZATION**: 需要检查用户权限
- **ERROR**: 发生系统错误

## 使用流程

### 1. 在ask方法中的调用流程

```java
@Override
public Flux<ServerSentEvent<String>> ask(AskDTO ask) throws JsonProcessingException {
    // ... 初始化代码 ...
    
    // 执行业务判断逻辑
    BusinessJudgmentResult judgmentResult = performBusinessJudgment(ask, chatProcessDTO);
    
    // 根据判断结果决定后续处理
    if (!judgmentResult.isOverallValid()) {
        // 检查是否需要中断流程
        if (businessJudgmentResultHandler.shouldInterruptFlow(judgmentResult)) {
            // 返回判断结果消息
            StreamMessageVO judgmentMessageVO = businessJudgmentResultHandler.handleJudgmentResult(ask, judgmentResult);
            // ... 返回响应 ...
        }
    }
    
    // 继续正常处理流程
    // ...
}
```

### 2. 判断结果处理

系统会根据判断结果自动生成用户友好的响应消息：

- ✅ **判断通过**: 显示成功消息，继续处理
- ❌ **判断失败**: 显示详细的问题分析和解决建议

### 3. 流程中断策略

系统会根据错误的严重程度决定是否中断流程：

- **CRITICAL**: 系统错误，必须中断
- **HIGH**: 权限或实例问题，需要中断
- **MEDIUM**: 材料或表单问题，可能中断
- **LOW**: 轻微问题，不中断

## 扩展和自定义

### 添加新的判断逻辑

1. 在BusinessJudgmentComponentEngine中添加新的检查方法：

```java
private boolean checkNewBusinessRule(ChatProcessDTO chatProcessDTO) {
    // 实现新的业务规则检查
    return true;
}
```

2. 在performBusinessJudgment方法中调用新的检查：

```java
boolean newRuleValid = checkNewBusinessRule(chatProcessDTO);
result.setNewRuleValid(newRuleValid);
```

3. 更新综合判断逻辑和下一步操作确定逻辑。

### 自定义响应消息

在BusinessJudgmentResultHandler中修改generateResponseMessage方法，自定义不同判断结果的响应消息格式。

### 配置判断规则

可以通过配置文件或数据库配置不同的判断规则，使组件更加灵活。

## 日志和监控

### 日志记录

系统会自动记录以下日志：

- 判断执行过程日志
- 判断结果日志（成功/失败）
- 详细的失败原因日志
- 流程中断日志

### 监控指标

建议监控以下指标：

- 判断成功率
- 各类判断失败的频率
- 流程中断率
- 判断执行时间

## 最佳实践

1. **性能优化**: 判断逻辑应该尽可能高效，避免复杂的数据库查询
2. **错误处理**: 确保所有异常都被正确捕获和处理
3. **日志记录**: 记录足够的日志信息用于问题排查
4. **测试覆盖**: 为所有判断逻辑编写单元测试
5. **配置管理**: 将可变的判断规则配置化

## 故障排查

### 常见问题

1. **判断总是失败**: 检查ChatProcessDTO是否正确设置
2. **权限检查失败**: 验证用户权限配置
3. **实例状态异常**: 检查实例初始化流程
4. **材料检查失败**: 确认材料上传和存储逻辑

### 调试方法

1. 启用DEBUG日志级别查看详细执行过程
2. 检查ChatCacheUtil中的数据是否正确
3. 验证ComponentDataContext的设置
4. 使用单元测试验证判断逻辑

## 总结

业务判断组件提供了一个灵活、可扩展的方式来在ask方法中执行业务逻辑判断。通过合理使用这个组件，可以提高系统的健壮性和用户体验，确保只有满足业务规则的请求才会被继续处理。
