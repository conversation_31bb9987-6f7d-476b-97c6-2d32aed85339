## 太仓不动产智慧受理平台 (ai-central-platform)

> 太仓不动产智慧受理平台是一个基于微服务架构的系统，用于处理不动产相关的业务受理流程。

> 该项目旨在提供高效、智能的不动产业务受理服务，实现业务流程的数字化和智能化。

> 项目处于开发阶段。

> 该项目由workplat团队开发。

> 基于SpringBoot 3.2.4、Spring Cloud 2023.0.1、Spring Cloud Alibaba 2023.0.1.0开发的Java微服务系统，使用Java 21。



## 依赖项 (Dependencies)

* Spring Boot (3.2.4): 容器+MVC框架
* Spring Cloud (2023.0.1): 微服务框架
* Spring Cloud Alibaba (2023.0.1.0): 阿里微服务框架
* MySQL Connector (9.0.0): MySQL数据库连接
* Dubbo: 微服务调用框架
* Sa-Token: 权限认证框架
* Logback: 日志框架
* Knife4j: 文档生产工具
* Fastjson2: JSON序列化工具
* Redis: 分布式缓存
* Caffeine: 内存缓存
* MongoDB: NoSql数据库
* Druid: 数据库连接池
* Lombok: 简化对象封装工具
* Guava: Google常用工具库
* Hutool: Java工具类库
* OkHttp: Java网络请求框架
* Deepoove (1.12.2): Word导出功能


## 开发环境

> 该项目需要以下开发环境：
> - JDK 21
> - Maven
> - MySQL数据库
> - Redis
> - MongoDB
> - Nacos (192.168.124.215:8848)
> 
> 启动项目时需要添加VM参数：--add-opens java.base/java.net=ALL-UNNAMED --add-opens java.base/sun.net.www.protocol.https=ALL-UNNAMED


## 项目结构 (Structure)

> 项目采用模块化设计，按功能划分为不同的模块

```
ai-central-platform/
├── accept-api/                 // 对外交互API、DTO层
│   ├── src/main/java/          // Java源代码目录
│   │   └── ...                 // API接口和DTO定义
│   └── pom.xml                 // 模块POM配置
├── accept-dubbo/               // 对内Dubbo服务层
│   ├── src/main/java/          // Java源代码目录
│   │   └── ...                 // Dubbo服务接口定义
│   └── pom.xml                 // 模块POM配置
├── accept-biz/                 // 业务实现层，包含启动类
│   ├── src/main/java/          // Java源代码目录
│   │   └── com/workplat/       // 项目主包
│   │       ├── accept/         // 业务受理核心实现
│   │       ├── inheritance/    // 继承相关功能
│   │       ├── utils/          // 工具类
│   │       └── AcceptApp.java  // 应用启动类
│   ├── src/main/resources/     // 资源文件
│   │   └── ...                 // 配置文件、静态资源等
│   └── pom.xml                 // 模块POM配置
├── docs/                       // 项目文档
├── pom.xml                     // 项目根POM文件，管理所有模块
└── README.md                   // 项目说明文档
```

## Structrue (init from project tree)

> It is essential to consistently refine the analysis down to the file level — this level of granularity is of utmost importance.

> If the number of files is too large, you should at least list all the directories, and provide comments for the parts you consider particularly important.

> In the code block below, add comments to the directories/files to explain their functionality and usage scenarios.

> if you think the directory/file is not important, you can not skip it, just add a simple comment to it.

> but if you think the directory/file is important, you should read the files and add more detail comments on it (e.g. add comments on the functions, classes, and variables. explain the functionality and usage scenarios. write the importance of the directory/file).

### AI对话相关接口

- 在`accept-biz`的`ChatApiImpl`中实现了两个主要接口：
  1. `componentRun`：用于执行组件运行，通过组件编码获取组件信息，然后使用组件引擎执行组件，并返回结果。
  2. `ask`：提供问答功能，通过调用`DifyServiceHelper`的`streamingMessage`方法实现流式响应，支持用户提问并获取AI回答。
- `DifyServiceHelper`封装了与Dify平台交互的各种方法，包括：
  - `streamingMessage`和`blockingMessage`方法用于聊天消息处理
  - `streamingWorkflow`和`blockingWorkflow`方法用于工作流处理
  - `getMessages`方法用于获取消息列表
  - `getConversations`方法用于获取会话列表
  - 其他辅助方法如停止任务等

## 项目概述
AI Central Platform 是一个集中式的AI应用平台，用于处理和管理各种AI相关的业务流程。

## 主要组件

### FormJsonProcessor
- 位置：`accept-biz/src/main/java/com/workplat/utils/FormJsonProcessor.java`
- 功能：处理表单JSON数据，支持字段过滤和值更新
- 特性：
  - 支持递归处理嵌套表单结构
  - 支持特殊组件（单选框、下拉框等）的值处理
  - 维护组件状态（选中状态等）
  - 保留必要的配置信息