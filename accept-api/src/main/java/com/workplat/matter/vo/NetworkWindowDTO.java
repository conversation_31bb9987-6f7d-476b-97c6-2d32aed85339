package com.workplat.matter.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR> <PERSON>eng
 * @package com.workplat.matter.vo
 * @description
 * @date 2025/6/10 13:53
 */
@Data
@Schema(description = "网点窗口DTO")
public class NetworkWindowDTO {

    @Schema(description = "id")
    private String id;

    @NotBlank(message = "网点名称不能为空")
    @Schema(description = "网点名称")
    private String networkName;

    @NotBlank(message = "编码不能为空")
    @Schema(description = "编码")
    private String code;

    @NotBlank(message = "窗口信息不能为空")
    @Schema(description = "窗口信息")
    private String windowInfo;

    @NotBlank(message = "网点经度不能为空")
    @Schema(description = "网点经度")
    private String longitude;

    @NotBlank(message = "网点纬度不能为空")
    @Schema(description = "网点纬度")
    private String latitude;
}
