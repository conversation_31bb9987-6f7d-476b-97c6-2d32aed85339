package com.workplat.flow;

import com.workplat.flow.dto.ConfFlowDTO;
import com.workplat.flow.dto.ProgressPercentageDTO;
import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.response.ResponseData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @ClassName ConfFlowApi
 * @Description
 * @Date 2025/5/22
 * @Version 1.0.0
 **/
@Validated
@RestController
@RequestMapping("/api/flow/flowable/modelInfo")
@Tag(name = "边聊边办流程配置")
public interface ConfFlowApi {


    /**
     * 保存流程
     *
     * @param
     * @return
     */
    @Operation(summary = "根据id查询单个流程的用户任务节点")
    @PostMapping("/save")
    ResponseData<Void> save(@Valid @RequestBody ConfFlowDTO confFlowDTO);

    /**
     * 保存流程
     *
     * @param
     * @return
     */
    @Operation(summary = "分页查询")
    @GetMapping("/page")
    ResponseData<Page<ConfFlowDTO>> page(PageableDTO pageDTO, String keyword);

    /**
     * id查询
     *
     * @param
     * @return
     */
    @Operation(summary = "id查询")
    @GetMapping("/queryById")
    ResponseData<ConfFlowDTO> queryById(String id);

    @Operation(summary = "code查询")
    @GetMapping("/queryByCode")
    ResponseData<ConfFlowDTO> queryByCode(String code);

    /**
     * id删除
     *
     * @param
     * @return
     */
    @Operation(summary = "id删除")
    @GetMapping("/deleteById")
    ResponseData<Void> deleteById(String id);


    @Operation(summary = "获取进度百分比")
    @PostMapping("/getProgressPercentage")
    ResponseData<String> getProgressPercentage(@RequestBody @Valid ProgressPercentageDTO dto);

}
