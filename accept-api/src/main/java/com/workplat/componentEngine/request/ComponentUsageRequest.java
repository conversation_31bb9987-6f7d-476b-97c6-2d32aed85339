package com.workplat.componentEngine.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR> cheng
 * @package com.workplat.flow
 * @description 组件使用查询Request
 * @date 2025/5/21 11:17
 */
@Data
@Schema(description = "组件使用查询Request")
public class ComponentUsageRequest {

    @Schema(description = "组件名称")
    private String name;

    @Schema(description = "页码 从1开始")
    @NotNull(message = "pageNumber不能为空")
    private Integer pageNumber;

    @Schema(description = "每页显示条数")
    @NotNull(message = "pageSize不能为空")
    private Integer pageSize;
}
