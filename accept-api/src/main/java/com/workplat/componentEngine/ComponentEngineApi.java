package com.workplat.componentEngine;

import com.workplat.componentEngine.vo.ComponentEngineVO;
import com.workplat.gss.common.core.response.ResponseData;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "组件引擎")
@RestController
@RequestMapping(value = "/api/component/engine")
public interface ComponentEngineApi {

    @RequestMapping("/list")
    ResponseData<List<ComponentEngineVO>> list();


}
