package com.workplat.componentEngine.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 情境组件配置模型
 * 用于前端配置不同业务类型的情境组件
 *
 * <AUTHOR>
 * @date 2025/05/13
 */
@Data
@Schema(name = "情境组件配置模型")
public class SituationComponentConfig {

    @Schema(description = "业务类型", example = "situation")
    private String businessType;
    
    @Schema(description = "额外参数")
    private Object extraParams;
} 