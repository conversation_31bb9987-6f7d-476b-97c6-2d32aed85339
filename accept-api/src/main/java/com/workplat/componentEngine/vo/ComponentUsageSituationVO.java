package com.workplat.componentEngine.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR> cheng
 * @package com.workplat.flow.vo
 * @description 组件使用情况
 * @date 2025/5/21 10:53
 */
@Data
@AllArgsConstructor
@Schema(description = "组件使用情况")
public class ComponentUsageSituationVO {

    @Schema(description = "组件id")
    private String id;

    @Schema(description = "组件code")
    private String code;

    @Schema(description = "组件名称")
    private String name;

    @Schema(description = "组件描述")
    private String description;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "所绑事项个数")
    private Integer itemCount;

}
