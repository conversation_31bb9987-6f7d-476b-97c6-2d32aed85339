package com.workplat.serve.dto;

import com.workplat.gss.common.core.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @className: com.workplat.serve.entity
 * @description: 服务办理方式
 * @date 2025/5/14 14:07
 */
@Setter
@Getter
@Schema(description = "办理方式dto")
public class BizServeTypeDto {
    @Schema(description = "办理方式id")
    private String id;
    @Schema(description = "服务方式")
    private String methodId;
    @Schema(description = "类型")
    private String type;
    @Schema(description = "图标")
    private String icon;
    @Schema(description = "描述")
    private String ms;
    @Schema(description = "地址")
    private String dz;





}
