package com.workplat.serve.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> cheng
 * @package com.workplat.serve.dto
 * @description
 * @date 2025/6/10 20:15
 */
@Data
@Schema(description = "服务方式默认值DTO")
public class MethodDefaultDTO {

    @Schema(description = "服务办理方式")
    private String method;

    @Schema(description = "默认图标的id")
    private String iconId;

    @Schema(description = "默认描述")
    private String description;
}
